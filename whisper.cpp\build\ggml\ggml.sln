﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{EC61222C-DAA4-3802-8E5A-5475F8210641}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{2FBDCE20-3095-380B-92BE-2CEB3B591A77}"
	ProjectSection(ProjectDependencies) = postProject
		{EC61222C-DAA4-3802-8E5A-5475F8210641} = {EC61222C-DAA4-3802-8E5A-5475F8210641}
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{1350ADEA-93CA-329F-A31E-6E0BA4C61381}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{329087FE-BA1C-36BD-A96C-7843776A63C5}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml", "src\ggml.vcxproj", "{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml-base", "src\ggml-base.vcxproj", "{697CAFA6-0EE1-3F69-AF49-3C5303F50007}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml-cpu", "src\ggml-cpu.vcxproj", "{54D2F21E-E179-3772-B611-9EDF7E8CA92F}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Debug|x64.ActiveCfg = Debug|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Debug|x64.Build.0 = Debug|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Release|x64.ActiveCfg = Release|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Release|x64.Build.0 = Release|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.Debug|x64.ActiveCfg = Debug|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.Release|x64.ActiveCfg = Release|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.Debug|x64.ActiveCfg = Debug|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.Release|x64.ActiveCfg = Release|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Debug|x64.ActiveCfg = Debug|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Debug|x64.Build.0 = Debug|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Release|x64.ActiveCfg = Release|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Release|x64.Build.0 = Release|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Debug|x64.ActiveCfg = Debug|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Debug|x64.Build.0 = Debug|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Release|x64.ActiveCfg = Release|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Release|x64.Build.0 = Release|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Debug|x64.ActiveCfg = Debug|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Debug|x64.Build.0 = Debug|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Release|x64.ActiveCfg = Release|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Release|x64.Build.0 = Release|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Debug|x64.ActiveCfg = Debug|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Debug|x64.Build.0 = Debug|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Release|x64.ActiveCfg = Release|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Release|x64.Build.0 = Release|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B408E62F-4005-330B-9762-19E800CBA430}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
