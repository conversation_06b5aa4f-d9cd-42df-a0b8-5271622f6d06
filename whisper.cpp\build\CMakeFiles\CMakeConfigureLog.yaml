
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
      Copyright (C) Microsoft Corporation. All rights reserved.
      
      Build started 02/07/2025 19:50:50.
      Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:04.12
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/4.1.0-rc1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/link.com"
    found: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lld-link"
      - "C:/Users/<USER>/bin/lld-link.com"
      - "C:/Users/<USER>/bin/lld-link.exe"
      - "C:/Users/<USER>/bin/lld-link"
      - "C:/Program Files/Git/mingw64/bin/lld-link.com"
      - "C:/Program Files/Git/mingw64/bin/lld-link.exe"
      - "C:/Program Files/Git/mingw64/bin/lld-link"
      - "C:/Program Files/Git/usr/local/bin/lld-link.com"
      - "C:/Program Files/Git/usr/local/bin/lld-link.exe"
      - "C:/Program Files/Git/usr/local/bin/lld-link"
      - "C:/Program Files/Git/usr/bin/lld-link.com"
      - "C:/Program Files/Git/usr/bin/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/lld-link"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/lld-link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/lld-link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/lld-link"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/lld-link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/lld-link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/lld-link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link.com"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/link.com"
    found: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/mt.com"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/mt"
      - "C:/Users/<USER>/bin/mt.com"
      - "C:/Users/<USER>/bin/mt.exe"
      - "C:/Users/<USER>/bin/mt"
      - "C:/Program Files/Git/mingw64/bin/mt.com"
      - "C:/Program Files/Git/mingw64/bin/mt.exe"
      - "C:/Program Files/Git/mingw64/bin/mt"
      - "C:/Program Files/Git/usr/local/bin/mt.com"
      - "C:/Program Files/Git/usr/local/bin/mt.exe"
      - "C:/Program Files/Git/usr/local/bin/mt"
      - "C:/Program Files/Git/usr/bin/mt.com"
      - "C:/Program Files/Git/usr/bin/mt.exe"
      - "C:/Program Files/Git/usr/bin/mt"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/mt.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/mt.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/mt"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/mt.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/mt.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/ProgramData/chocolatey/bin/mt.com"
      - "C:/ProgramData/chocolatey/bin/mt.exe"
      - "C:/ProgramData/chocolatey/bin/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/mt.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/mt.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/mt"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/mt.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/mt.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/mt"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
      - "C:/Program Files/Git/usr/bin/vendor_perl/mt.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/mt.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/mt"
      - "C:/Program Files/Git/usr/bin/core_perl/mt.com"
      - "C:/Program Files/Git/usr/bin/core_perl/mt.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
      Copyright (C) Microsoft Corporation. All rights reserved.
      
      Build started 02/07/2025 19:50:55.
      Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.04
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lld-link"
      - "C:/Users/<USER>/bin/lld-link.com"
      - "C:/Users/<USER>/bin/lld-link.exe"
      - "C:/Users/<USER>/bin/lld-link"
      - "C:/Program Files/Git/mingw64/bin/lld-link.com"
      - "C:/Program Files/Git/mingw64/bin/lld-link.exe"
      - "C:/Program Files/Git/mingw64/bin/lld-link"
      - "C:/Program Files/Git/usr/local/bin/lld-link.com"
      - "C:/Program Files/Git/usr/local/bin/lld-link.exe"
      - "C:/Program Files/Git/usr/local/bin/lld-link"
      - "C:/Program Files/Git/usr/bin/lld-link.com"
      - "C:/Program Files/Git/usr/bin/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/lld-link"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/lld-link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/lld-link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/lld-link"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/lld-link.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/lld-link.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/lld-link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/lld-link"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link.com"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Users/<USER>/bin/rc.com"
      - "C:/Users/<USER>/bin/rc.exe"
      - "C:/Users/<USER>/bin/rc"
      - "C:/Program Files/Git/mingw64/bin/rc.com"
      - "C:/Program Files/Git/mingw64/bin/rc.exe"
      - "C:/Program Files/Git/mingw64/bin/rc"
      - "C:/Program Files/Git/usr/local/bin/rc.com"
      - "C:/Program Files/Git/usr/local/bin/rc.exe"
      - "C:/Program Files/Git/usr/local/bin/rc"
      - "C:/Program Files/Git/usr/bin/rc.com"
      - "C:/Program Files/Git/usr/bin/rc.exe"
      - "C:/Program Files/Git/usr/bin/rc"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/rc.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/rc.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/rc"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/rc.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/rc.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/ProgramData/chocolatey/bin/rc.com"
      - "C:/ProgramData/chocolatey/bin/rc.exe"
      - "C:/ProgramData/chocolatey/bin/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/rc.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/rc.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/rc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/rc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/rc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/rc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/Program Files/Git/usr/bin/vendor_perl/rc.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/rc.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/rc"
      - "C:/Program Files/Git/usr/bin/core_perl/rc.com"
      - "C:/Program Files/Git/usr/bin/core_perl/rc.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/whisper.cpp/bin/rc.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/rc.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/rc"
      - "C:/Program Files (x86)/whisper.cpp/sbin/rc.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/rc.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/rc"
      - "C:/Program Files (x86)/whisper.cpp/rc.com"
      - "C:/Program Files (x86)/whisper.cpp/rc.exe"
      - "C:/Program Files (x86)/whisper.cpp/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pz0ire"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pz0ire"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pz0ire'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_b019b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:50:57.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pz0ire\\cmTC_b019b.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b019b.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pz0ire\\Debug\\".
          Creating directory "cmTC_b019b.dir\\Debug\\cmTC_b019b.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b019b.dir\\Debug\\cmTC_b019b.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_b019b.dir\\Debug\\\\" /Fd"cmTC_b019b.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          CMakeCCompilerABI.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_b019b.dir\\Debug\\\\" /Fd"cmTC_b019b.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pz0ire\\Debug\\cmTC_b019b.exe" /INCREMENTAL /ILK:"cmTC_b019b.dir\\Debug\\cmTC_b019b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pz0ire/Debug/cmTC_b019b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pz0ire/Debug/cmTC_b019b.lib" /MACHINE:X64  /machine:x64 cmTC_b019b.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_b019b.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pz0ire\\Debug\\cmTC_b019b.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_b019b.dir\\Debug\\cmTC_b019b.tlog\\unsuccessfulbuild".
          Touching "cmTC_b019b.dir\\Debug\\cmTC_b019b.tlog\\cmTC_b019b.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pz0ire\\cmTC_b019b.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.78
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-bs96bg"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-bs96bg"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-bs96bg'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_41c16.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:50:59.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bs96bg\\cmTC_41c16.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_41c16.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bs96bg\\Debug\\".
          Creating directory "cmTC_41c16.dir\\Debug\\cmTC_41c16.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_41c16.dir\\Debug\\cmTC_41c16.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_41c16.dir\\Debug\\\\" /Fd"cmTC_41c16.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          CMakeCXXCompilerABI.cpp
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_41c16.dir\\Debug\\\\" /Fd"cmTC_41c16.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bs96bg\\Debug\\cmTC_41c16.exe" /INCREMENTAL /ILK:"cmTC_41c16.dir\\Debug\\cmTC_41c16.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-bs96bg/Debug/cmTC_41c16.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-bs96bg/Debug/cmTC_41c16.lib" /MACHINE:X64  /machine:x64 cmTC_41c16.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_41c16.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bs96bg\\Debug\\cmTC_41c16.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_41c16.dir\\Debug\\cmTC_41c16.tlog\\unsuccessfulbuild".
          Touching "cmTC_41c16.dir\\Debug\\cmTC_41c16.tlog\\cmTC_41c16.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bs96bg\\cmTC_41c16.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.61
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindGit.cmake:86 (find_program)"
      - "cmake/git-vars.cmake:1 (find_package)"
      - "CMakeLists.txt:26 (include)"
    mode: "program"
    variable: "GIT_EXECUTABLE"
    description: "Git command line client"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git.cmd"
      - "git"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/"
    searched_directories:
      - "C:/Users/<USER>/bin/git.cmd.com"
      - "C:/Users/<USER>/bin/git.cmd.exe"
      - "C:/Users/<USER>/bin/git.cmd"
      - "C:/Program Files/Git/mingw64/bin/git.cmd.com"
      - "C:/Program Files/Git/mingw64/bin/git.cmd.exe"
      - "C:/Program Files/Git/mingw64/bin/git.cmd"
      - "C:/Program Files/Git/usr/local/bin/git.cmd.com"
      - "C:/Program Files/Git/usr/local/bin/git.cmd.exe"
      - "C:/Program Files/Git/usr/local/bin/git.cmd"
      - "C:/Program Files/Git/usr/bin/git.cmd.com"
      - "C:/Program Files/Git/usr/bin/git.cmd.exe"
      - "C:/Program Files/Git/usr/bin/git.cmd"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/git.cmd.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/git.cmd.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/git.cmd"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/git.cmd.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/git.cmd.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/git.cmd"
      - "C:/Windows/System32/git.cmd.com"
      - "C:/Windows/System32/git.cmd.exe"
      - "C:/Windows/System32/git.cmd"
      - "C:/Windows/git.cmd.com"
      - "C:/Windows/git.cmd.exe"
      - "C:/Windows/git.cmd"
      - "C:/Windows/System32/wbem/git.cmd.com"
      - "C:/Windows/System32/wbem/git.cmd.exe"
      - "C:/Windows/System32/wbem/git.cmd"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd"
      - "C:/Windows/System32/OpenSSH/git.cmd.com"
      - "C:/Windows/System32/OpenSSH/git.cmd.exe"
      - "C:/Windows/System32/OpenSSH/git.cmd"
      - "C:/Program Files/nodejs/git.cmd.com"
      - "C:/Program Files/nodejs/git.cmd.exe"
      - "C:/Program Files/nodejs/git.cmd"
      - "C:/ProgramData/chocolatey/bin/git.cmd.com"
      - "C:/ProgramData/chocolatey/bin/git.cmd.exe"
      - "C:/ProgramData/chocolatey/bin/git.cmd"
      - "C:/Program Files/Git/cmd/git.cmd.com"
      - "C:/Program Files/Git/cmd/git.cmd.exe"
      - "C:/Program Files/Git/cmd/git.cmd"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/git.cmd.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/git.cmd.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/git.cmd"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/git.cmd.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/git.cmd.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/git.cmd"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/git.cmd.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/git.cmd.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/git.cmd"
      - "C:/Program Files/CMake/bin/git.cmd.com"
      - "C:/Program Files/CMake/bin/git.cmd.exe"
      - "C:/Program Files/CMake/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Roaming/npm/git.cmd.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/git.cmd"
      - "C:/Program Files/Git/usr/bin/vendor_perl/git.cmd.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/git.cmd.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/git.cmd"
      - "C:/Program Files/Git/usr/bin/core_perl/git.cmd.com"
      - "C:/Program Files/Git/usr/bin/core_perl/git.cmd.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/git.cmd"
      - "C:/Program Files/bin/git.cmd.com"
      - "C:/Program Files/bin/git.cmd.exe"
      - "C:/Program Files/bin/git.cmd"
      - "C:/Program Files/sbin/git.cmd.com"
      - "C:/Program Files/sbin/git.cmd.exe"
      - "C:/Program Files/sbin/git.cmd"
      - "C:/Program Files/git.cmd.com"
      - "C:/Program Files/git.cmd.exe"
      - "C:/Program Files/git.cmd"
      - "C:/Program Files (x86)/bin/git.cmd.com"
      - "C:/Program Files (x86)/bin/git.cmd.exe"
      - "C:/Program Files (x86)/bin/git.cmd"
      - "C:/Program Files (x86)/sbin/git.cmd.com"
      - "C:/Program Files (x86)/sbin/git.cmd.exe"
      - "C:/Program Files (x86)/sbin/git.cmd"
      - "C:/Program Files (x86)/git.cmd.com"
      - "C:/Program Files (x86)/git.cmd.exe"
      - "C:/Program Files (x86)/git.cmd"
      - "C:/Program Files/CMake/bin/git.cmd.com"
      - "C:/Program Files/CMake/bin/git.cmd.exe"
      - "C:/Program Files/CMake/bin/git.cmd"
      - "C:/Program Files/CMake/sbin/git.cmd.com"
      - "C:/Program Files/CMake/sbin/git.cmd.exe"
      - "C:/Program Files/CMake/sbin/git.cmd"
      - "C:/Program Files/CMake/git.cmd.com"
      - "C:/Program Files/CMake/git.cmd.exe"
      - "C:/Program Files/CMake/git.cmd"
      - "C:/Program Files (x86)/whisper.cpp/bin/git.cmd.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/git.cmd.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/git.cmd"
      - "C:/Program Files (x86)/whisper.cpp/sbin/git.cmd.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/git.cmd.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/git.cmd"
      - "C:/Program Files (x86)/whisper.cpp/git.cmd.com"
      - "C:/Program Files (x86)/whisper.cpp/git.cmd.exe"
      - "C:/Program Files (x86)/whisper.cpp/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd"
      - "C:/Users/<USER>/bin/git.com"
      - "C:/Users/<USER>/bin/git.exe"
      - "C:/Users/<USER>/bin/git"
      - "C:/Program Files/Git/mingw64/bin/git.com"
    found: "C:/Program Files/Git/mingw64/bin/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "ggml/CMakeLists.txt:230 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cclwzy"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cclwzy"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cclwzy'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_d175a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:00.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\cmTC_d175a.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d175a.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\Debug\\".
          Creating directory "cmTC_d175a.dir\\Debug\\cmTC_d175a.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d175a.dir\\Debug\\cmTC_d175a.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_d175a.dir\\Debug\\\\" /Fd"cmTC_d175a.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          src.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_d175a.dir\\Debug\\\\" /Fd"cmTC_d175a.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\src.c"
        C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\src.c(1,1): fatal error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\cmTC_d175a.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\cmTC_d175a.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\cmTC_d175a.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\src.c(1,1): fatal error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cclwzy\\cmTC_d175a.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.44
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLibraryExists.cmake:154 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "ggml/CMakeLists.txt:230 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7vumcq"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7vumcq"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7vumcq'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_f4761.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:01.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\cmTC_f4761.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f4761.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\Debug\\".
          Creating directory "cmTC_f4761.dir\\Debug\\cmTC_f4761.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f4761.dir\\Debug\\cmTC_f4761.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_f4761.dir\\Debug\\\\" /Fd"cmTC_f4761.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          CheckFunctionExists.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_f4761.dir\\Debug\\\\" /Fd"cmTC_f4761.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\CheckFunctionExists.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\Debug\\cmTC_f4761.exe" /INCREMENTAL /ILK:"cmTC_f4761.dir\\Debug\\cmTC_f4761.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7vumcq/Debug/cmTC_f4761.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7vumcq/Debug/cmTC_f4761.lib" /MACHINE:X64  /machine:x64 cmTC_f4761.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\cmTC_f4761.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\cmTC_f4761.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\cmTC_f4761.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7vumcq\\cmTC_f4761.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.48
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLibraryExists.cmake:154 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "ggml/CMakeLists.txt:230 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aodou5"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aodou5"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aodou5'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_b56e6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:02.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\cmTC_b56e6.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b56e6.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\Debug\\".
          Creating directory "cmTC_b56e6.dir\\Debug\\cmTC_b56e6.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b56e6.dir\\Debug\\cmTC_b56e6.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_b56e6.dir\\Debug\\\\" /Fd"cmTC_b56e6.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          CheckFunctionExists.c
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_b56e6.dir\\Debug\\\\" /Fd"cmTC_b56e6.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\CheckFunctionExists.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\Debug\\cmTC_b56e6.exe" /INCREMENTAL /ILK:"cmTC_b56e6.dir\\Debug\\cmTC_b56e6.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aodou5/Debug/cmTC_b56e6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aodou5/Debug/cmTC_b56e6.lib" /MACHINE:X64  /machine:x64 cmTC_b56e6.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\cmTC_b56e6.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\cmTC_b56e6.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\cmTC_b56e6.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aodou5\\cmTC_b56e6.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.49
        
      exitCode: 1
  -
    kind: "find-v1"
    backtrace:
      - "ggml/src/CMakeLists.txt:69 (find_program)"
    mode: "program"
    variable: "GGML_CCACHE_FOUND"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ccache"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Users/<USER>/bin/ccache.com"
      - "C:/Users/<USER>/bin/ccache.exe"
      - "C:/Users/<USER>/bin/ccache"
      - "C:/Program Files/Git/mingw64/bin/ccache.com"
      - "C:/Program Files/Git/mingw64/bin/ccache.exe"
      - "C:/Program Files/Git/mingw64/bin/ccache"
      - "C:/Program Files/Git/usr/local/bin/ccache.com"
      - "C:/Program Files/Git/usr/local/bin/ccache.exe"
      - "C:/Program Files/Git/usr/local/bin/ccache"
      - "C:/Program Files/Git/usr/bin/ccache.com"
      - "C:/Program Files/Git/usr/bin/ccache.exe"
      - "C:/Program Files/Git/usr/bin/ccache"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/ccache.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/ccache.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/ccache"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/ccache.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/ccache.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/ccache"
      - "C:/Windows/System32/ccache.com"
      - "C:/Windows/System32/ccache.exe"
      - "C:/Windows/System32/ccache"
      - "C:/Windows/ccache.com"
      - "C:/Windows/ccache.exe"
      - "C:/Windows/ccache"
      - "C:/Windows/System32/wbem/ccache.com"
      - "C:/Windows/System32/wbem/ccache.exe"
      - "C:/Windows/System32/wbem/ccache"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ccache.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ccache.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ccache"
      - "C:/Windows/System32/OpenSSH/ccache.com"
      - "C:/Windows/System32/OpenSSH/ccache.exe"
      - "C:/Windows/System32/OpenSSH/ccache"
      - "C:/Program Files/nodejs/ccache.com"
      - "C:/Program Files/nodejs/ccache.exe"
      - "C:/Program Files/nodejs/ccache"
      - "C:/ProgramData/chocolatey/bin/ccache.com"
      - "C:/ProgramData/chocolatey/bin/ccache.exe"
      - "C:/ProgramData/chocolatey/bin/ccache"
      - "C:/Program Files/Git/cmd/ccache.com"
      - "C:/Program Files/Git/cmd/ccache.exe"
      - "C:/Program Files/Git/cmd/ccache"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/ccache.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/ccache.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/ccache"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/ccache.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/ccache.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/ccache"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ccache.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ccache.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ccache"
      - "C:/Program Files/CMake/bin/ccache.com"
      - "C:/Program Files/CMake/bin/ccache.exe"
      - "C:/Program Files/CMake/bin/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ccache"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/ccache"
      - "C:/Users/<USER>/AppData/Roaming/npm/ccache.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ccache.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ccache"
      - "C:/Program Files/Git/usr/bin/vendor_perl/ccache.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/ccache.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/ccache"
      - "C:/Program Files/Git/usr/bin/core_perl/ccache.com"
      - "C:/Program Files/Git/usr/bin/core_perl/ccache.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/ccache"
      - "C:/Program Files/bin/ccache.com"
      - "C:/Program Files/bin/ccache.exe"
      - "C:/Program Files/bin/ccache"
      - "C:/Program Files/sbin/ccache.com"
      - "C:/Program Files/sbin/ccache.exe"
      - "C:/Program Files/sbin/ccache"
      - "C:/Program Files/ccache.com"
      - "C:/Program Files/ccache.exe"
      - "C:/Program Files/ccache"
      - "C:/Program Files (x86)/bin/ccache.com"
      - "C:/Program Files (x86)/bin/ccache.exe"
      - "C:/Program Files (x86)/bin/ccache"
      - "C:/Program Files (x86)/sbin/ccache.com"
      - "C:/Program Files (x86)/sbin/ccache.exe"
      - "C:/Program Files (x86)/sbin/ccache"
      - "C:/Program Files (x86)/ccache.com"
      - "C:/Program Files (x86)/ccache.exe"
      - "C:/Program Files (x86)/ccache"
      - "C:/Program Files/CMake/bin/ccache.com"
      - "C:/Program Files/CMake/bin/ccache.exe"
      - "C:/Program Files/CMake/bin/ccache"
      - "C:/Program Files/CMake/sbin/ccache.com"
      - "C:/Program Files/CMake/sbin/ccache.exe"
      - "C:/Program Files/CMake/sbin/ccache"
      - "C:/Program Files/CMake/ccache.com"
      - "C:/Program Files/CMake/ccache.exe"
      - "C:/Program Files/CMake/ccache"
      - "C:/Program Files (x86)/whisper.cpp/bin/ccache.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/ccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/ccache"
      - "C:/Program Files (x86)/whisper.cpp/sbin/ccache.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/ccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/ccache"
      - "C:/Program Files (x86)/whisper.cpp/ccache.com"
      - "C:/Program Files (x86)/whisper.cpp/ccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/ccache"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "ggml/src/CMakeLists.txt:70 (find_program)"
    mode: "program"
    variable: "GGML_SCCACHE_FOUND"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "sccache"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Users/<USER>/bin/sccache.com"
      - "C:/Users/<USER>/bin/sccache.exe"
      - "C:/Users/<USER>/bin/sccache"
      - "C:/Program Files/Git/mingw64/bin/sccache.com"
      - "C:/Program Files/Git/mingw64/bin/sccache.exe"
      - "C:/Program Files/Git/mingw64/bin/sccache"
      - "C:/Program Files/Git/usr/local/bin/sccache.com"
      - "C:/Program Files/Git/usr/local/bin/sccache.exe"
      - "C:/Program Files/Git/usr/local/bin/sccache"
      - "C:/Program Files/Git/usr/bin/sccache.com"
      - "C:/Program Files/Git/usr/bin/sccache.exe"
      - "C:/Program Files/Git/usr/bin/sccache"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/sccache.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/sccache.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/sccache"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/sccache.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/sccache.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/sccache"
      - "C:/Windows/System32/sccache.com"
      - "C:/Windows/System32/sccache.exe"
      - "C:/Windows/System32/sccache"
      - "C:/Windows/sccache.com"
      - "C:/Windows/sccache.exe"
      - "C:/Windows/sccache"
      - "C:/Windows/System32/wbem/sccache.com"
      - "C:/Windows/System32/wbem/sccache.exe"
      - "C:/Windows/System32/wbem/sccache"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/sccache.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/sccache.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/sccache"
      - "C:/Windows/System32/OpenSSH/sccache.com"
      - "C:/Windows/System32/OpenSSH/sccache.exe"
      - "C:/Windows/System32/OpenSSH/sccache"
      - "C:/Program Files/nodejs/sccache.com"
      - "C:/Program Files/nodejs/sccache.exe"
      - "C:/Program Files/nodejs/sccache"
      - "C:/ProgramData/chocolatey/bin/sccache.com"
      - "C:/ProgramData/chocolatey/bin/sccache.exe"
      - "C:/ProgramData/chocolatey/bin/sccache"
      - "C:/Program Files/Git/cmd/sccache.com"
      - "C:/Program Files/Git/cmd/sccache.exe"
      - "C:/Program Files/Git/cmd/sccache"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/sccache.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/sccache.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/sccache"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/sccache.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/sccache.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/sccache"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/sccache.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/sccache.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/sccache"
      - "C:/Program Files/CMake/bin/sccache.com"
      - "C:/Program Files/CMake/bin/sccache.exe"
      - "C:/Program Files/CMake/bin/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/sccache"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/sccache"
      - "C:/Users/<USER>/AppData/Roaming/npm/sccache.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/sccache.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/sccache"
      - "C:/Program Files/Git/usr/bin/vendor_perl/sccache.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/sccache.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/sccache"
      - "C:/Program Files/Git/usr/bin/core_perl/sccache.com"
      - "C:/Program Files/Git/usr/bin/core_perl/sccache.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/sccache"
      - "C:/Program Files/bin/sccache.com"
      - "C:/Program Files/bin/sccache.exe"
      - "C:/Program Files/bin/sccache"
      - "C:/Program Files/sbin/sccache.com"
      - "C:/Program Files/sbin/sccache.exe"
      - "C:/Program Files/sbin/sccache"
      - "C:/Program Files/sccache.com"
      - "C:/Program Files/sccache.exe"
      - "C:/Program Files/sccache"
      - "C:/Program Files (x86)/bin/sccache.com"
      - "C:/Program Files (x86)/bin/sccache.exe"
      - "C:/Program Files (x86)/bin/sccache"
      - "C:/Program Files (x86)/sbin/sccache.com"
      - "C:/Program Files (x86)/sbin/sccache.exe"
      - "C:/Program Files (x86)/sbin/sccache"
      - "C:/Program Files (x86)/sccache.com"
      - "C:/Program Files (x86)/sccache.exe"
      - "C:/Program Files (x86)/sccache"
      - "C:/Program Files/CMake/bin/sccache.com"
      - "C:/Program Files/CMake/bin/sccache.exe"
      - "C:/Program Files/CMake/bin/sccache"
      - "C:/Program Files/CMake/sbin/sccache.com"
      - "C:/Program Files/CMake/sbin/sccache.exe"
      - "C:/Program Files/CMake/sbin/sccache"
      - "C:/Program Files/CMake/sccache.com"
      - "C:/Program Files/CMake/sccache.exe"
      - "C:/Program Files/CMake/sccache"
      - "C:/Program Files (x86)/whisper.cpp/bin/sccache.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/sccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/sccache"
      - "C:/Program Files (x86)/whisper.cpp/sbin/sccache.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/sccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/sccache"
      - "C:/Program Files (x86)/whisper.cpp/sccache.com"
      - "C:/Program Files (x86)/whisper.cpp/sccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/sccache"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:251 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:558 (_OPENMP_GET_FLAGS)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-t0fbuz"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-t0fbuz"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-t0fbuz'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_6b075.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:03.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-t0fbuz\\cmTC_6b075.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6b075.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-t0fbuz\\Debug\\".
          Creating directory "cmTC_6b075.dir\\Debug\\cmTC_6b075.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6b075.dir\\Debug\\cmTC_6b075.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_6b075.dir\\Debug\\\\" /Fd"cmTC_6b075.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-t0fbuz\\OpenMPTryFlag.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          OpenMPTryFlag.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_6b075.dir\\Debug\\\\" /Fd"cmTC_6b075.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-t0fbuz\\OpenMPTryFlag.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-t0fbuz\\Debug\\cmTC_6b075.exe" /INCREMENTAL /ILK:"cmTC_6b075.dir\\Debug\\cmTC_6b075.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-t0fbuz/Debug/cmTC_6b075.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-t0fbuz/Debug/cmTC_6b075.lib" /MACHINE:X64  /machine:x64 cmTC_6b075.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_6b075.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-t0fbuz\\Debug\\cmTC_6b075.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_6b075.dir\\Debug\\cmTC_6b075.tlog\\unsuccessfulbuild".
          Touching "cmTC_6b075.dir\\Debug\\cmTC_6b075.tlog\\cmTC_6b075.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-t0fbuz\\cmTC_6b075.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:251 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:558 (_OPENMP_GET_FLAGS)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-lw5h5h"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-lw5h5h"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-lw5h5h'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_1edf3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:04.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lw5h5h\\cmTC_1edf3.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_1edf3.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lw5h5h\\Debug\\".
          Creating directory "cmTC_1edf3.dir\\Debug\\cmTC_1edf3.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_1edf3.dir\\Debug\\cmTC_1edf3.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_1edf3.dir\\Debug\\\\" /Fd"cmTC_1edf3.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lw5h5h\\OpenMPTryFlag.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          OpenMPTryFlag.cpp
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_1edf3.dir\\Debug\\\\" /Fd"cmTC_1edf3.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lw5h5h\\OpenMPTryFlag.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lw5h5h\\Debug\\cmTC_1edf3.exe" /INCREMENTAL /ILK:"cmTC_1edf3.dir\\Debug\\cmTC_1edf3.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-lw5h5h/Debug/cmTC_1edf3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-lw5h5h/Debug/cmTC_1edf3.lib" /MACHINE:X64  /machine:x64 cmTC_1edf3.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_1edf3.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lw5h5h\\Debug\\cmTC_1edf3.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_1edf3.dir\\Debug\\cmTC_1edf3.tlog\\unsuccessfulbuild".
          Touching "cmTC_1edf3.dir\\Debug\\cmTC_1edf3.tlog\\cmTC_1edf3.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lw5h5h\\cmTC_1edf3.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.78
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:492 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:632 (_OPENMP_GET_SPEC_DATE)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP version"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-jrg7mx"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-jrg7mx"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-jrg7mx'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_5b465.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:05.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jrg7mx\\cmTC_5b465.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_5b465.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jrg7mx\\Debug\\".
          Creating directory "cmTC_5b465.dir\\Debug\\cmTC_5b465.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_5b465.dir\\Debug\\cmTC_5b465.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_5b465.dir\\Debug\\\\" /Fd"cmTC_5b465.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jrg7mx\\OpenMPCheckVersion.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          OpenMPCheckVersion.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_5b465.dir\\Debug\\\\" /Fd"cmTC_5b465.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jrg7mx\\OpenMPCheckVersion.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jrg7mx\\Debug\\cmTC_5b465.exe" /INCREMENTAL /ILK:"cmTC_5b465.dir\\Debug\\cmTC_5b465.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-jrg7mx/Debug/cmTC_5b465.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-jrg7mx/Debug/cmTC_5b465.lib" /MACHINE:X64  /machine:x64 cmTC_5b465.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_5b465.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jrg7mx\\Debug\\cmTC_5b465.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_5b465.dir\\Debug\\cmTC_5b465.tlog\\unsuccessfulbuild".
          Touching "cmTC_5b465.dir\\Debug\\cmTC_5b465.tlog\\cmTC_5b465.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jrg7mx\\cmTC_5b465.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.67
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:492 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:632 (_OPENMP_GET_SPEC_DATE)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-1x6xbk"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-1x6xbk"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-1x6xbk'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_d9255.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:06.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1x6xbk\\cmTC_d9255.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d9255.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1x6xbk\\Debug\\".
          Creating directory "cmTC_d9255.dir\\Debug\\cmTC_d9255.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d9255.dir\\Debug\\cmTC_d9255.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_d9255.dir\\Debug\\\\" /Fd"cmTC_d9255.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1x6xbk\\OpenMPCheckVersion.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          OpenMPCheckVersion.cpp
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_d9255.dir\\Debug\\\\" /Fd"cmTC_d9255.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1x6xbk\\OpenMPCheckVersion.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1x6xbk\\Debug\\cmTC_d9255.exe" /INCREMENTAL /ILK:"cmTC_d9255.dir\\Debug\\cmTC_d9255.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-1x6xbk/Debug/cmTC_d9255.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-1x6xbk/Debug/cmTC_d9255.lib" /MACHINE:X64  /machine:x64 cmTC_d9255.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_d9255.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1x6xbk\\Debug\\cmTC_d9255.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_d9255.dir\\Debug\\cmTC_d9255.tlog\\unsuccessfulbuild".
          Touching "cmTC_d9255.dir\\Debug\\cmTC_d9255.tlog\\cmTC_d9255.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1x6xbk\\cmTC_d9255.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_1"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-q6yi0e"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-q6yi0e"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-q6yi0e'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_c1c1e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:07.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q6yi0e\\cmTC_c1c1e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c1c1e.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q6yi0e\\Debug\\".
          Creating directory "cmTC_c1c1e.dir\\Debug\\cmTC_c1c1e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c1c1e.dir\\Debug\\cmTC_c1c1e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_c1c1e.dir\\Debug\\\\" /Fd"cmTC_c1c1e.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q6yi0e\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          src.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_c1c1e.dir\\Debug\\\\" /Fd"cmTC_c1c1e.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q6yi0e\\src.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q6yi0e\\Debug\\cmTC_c1c1e.exe" /INCREMENTAL /ILK:"cmTC_c1c1e.dir\\Debug\\cmTC_c1c1e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-q6yi0e/Debug/cmTC_c1c1e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-q6yi0e/Debug/cmTC_c1c1e.lib" /MACHINE:X64  /machine:x64 cmTC_c1c1e.dir\\Debug\\src.obj
          cmTC_c1c1e.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q6yi0e\\Debug\\cmTC_c1c1e.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c1c1e.dir\\Debug\\cmTC_c1c1e.tlog\\unsuccessfulbuild".
          Touching "cmTC_c1c1e.dir\\Debug\\cmTC_c1c1e.tlog\\cmTC_c1c1e.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q6yi0e\\cmTC_c1c1e.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.68
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_1"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aylzf3"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aylzf3"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aylzf3'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_0296e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:10.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aylzf3\\cmTC_0296e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_0296e.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aylzf3\\Debug\\".
          Creating directory "cmTC_0296e.dir\\Debug\\cmTC_0296e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_0296e.dir\\Debug\\cmTC_0296e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_0296e.dir\\Debug\\\\" /Fd"cmTC_0296e.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aylzf3\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          src.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_0296e.dir\\Debug\\\\" /Fd"cmTC_0296e.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aylzf3\\src.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aylzf3\\Debug\\cmTC_0296e.exe" /INCREMENTAL /ILK:"cmTC_0296e.dir\\Debug\\cmTC_0296e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aylzf3/Debug/cmTC_0296e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-aylzf3/Debug/cmTC_0296e.lib" /MACHINE:X64  /machine:x64 cmTC_0296e.dir\\Debug\\src.obj
          cmTC_0296e.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aylzf3\\Debug\\cmTC_0296e.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_0296e.dir\\Debug\\cmTC_0296e.tlog\\unsuccessfulbuild".
          Touching "cmTC_0296e.dir\\Debug\\cmTC_0296e.tlog\\cmTC_0296e.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-aylzf3\\cmTC_0296e.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.79
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_1"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-ih71t6"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-ih71t6"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-ih71t6'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_09a3b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:12.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ih71t6\\cmTC_09a3b.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_09a3b.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ih71t6\\Debug\\".
          Creating directory "cmTC_09a3b.dir\\Debug\\cmTC_09a3b.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_09a3b.dir\\Debug\\cmTC_09a3b.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_09a3b.dir\\Debug\\\\" /Fd"cmTC_09a3b.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ih71t6\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          src.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_09a3b.dir\\Debug\\\\" /Fd"cmTC_09a3b.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ih71t6\\src.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ih71t6\\Debug\\cmTC_09a3b.exe" /INCREMENTAL /ILK:"cmTC_09a3b.dir\\Debug\\cmTC_09a3b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-ih71t6/Debug/cmTC_09a3b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-ih71t6/Debug/cmTC_09a3b.lib" /MACHINE:X64  /machine:x64 cmTC_09a3b.dir\\Debug\\src.obj
          cmTC_09a3b.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ih71t6\\Debug\\cmTC_09a3b.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_09a3b.dir\\Debug\\cmTC_09a3b.tlog\\unsuccessfulbuild".
          Touching "cmTC_09a3b.dir\\Debug\\cmTC_09a3b.tlog\\cmTC_09a3b.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ih71t6\\cmTC_09a3b.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.70
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_1"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-p7taff"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-p7taff"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-p7taff'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_cfbc9.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:14.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p7taff\\cmTC_cfbc9.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_cfbc9.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p7taff\\Debug\\".
          Creating directory "cmTC_cfbc9.dir\\Debug\\cmTC_cfbc9.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_cfbc9.dir\\Debug\\cmTC_cfbc9.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_cfbc9.dir\\Debug\\\\" /Fd"cmTC_cfbc9.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p7taff\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          src.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_cfbc9.dir\\Debug\\\\" /Fd"cmTC_cfbc9.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p7taff\\src.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p7taff\\Debug\\cmTC_cfbc9.exe" /INCREMENTAL /ILK:"cmTC_cfbc9.dir\\Debug\\cmTC_cfbc9.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-p7taff/Debug/cmTC_cfbc9.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-p7taff/Debug/cmTC_cfbc9.lib" /MACHINE:X64  /machine:x64 cmTC_cfbc9.dir\\Debug\\src.obj
          cmTC_cfbc9.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p7taff\\Debug\\cmTC_cfbc9.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_cfbc9.dir\\Debug\\cmTC_cfbc9.tlog\\unsuccessfulbuild".
          Touching "cmTC_cfbc9.dir\\Debug\\cmTC_cfbc9.tlog\\cmTC_cfbc9.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p7taff\\cmTC_cfbc9.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.64
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_2"
    directories:
      source: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-esh3k0"
      binary: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-esh3k0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-esh3k0'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_f71f9.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 02/07/2025 19:51:17.
        Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-esh3k0\\cmTC_f71f9.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f71f9.dir\\Debug\\".
          Creating directory "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-esh3k0\\Debug\\".
          Creating directory "cmTC_f71f9.dir\\Debug\\cmTC_f71f9.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f71f9.dir\\Debug\\cmTC_f71f9.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /std:c11 /Fo"cmTC_f71f9.dir\\Debug\\\\" /Fd"cmTC_f71f9.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-esh3k0\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          src.c
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /std:c11 /Fo"cmTC_f71f9.dir\\Debug\\\\" /Fd"cmTC_f71f9.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-esh3k0\\src.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-esh3k0\\Debug\\cmTC_f71f9.exe" /INCREMENTAL /ILK:"cmTC_f71f9.dir\\Debug\\cmTC_f71f9.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-esh3k0/Debug/cmTC_f71f9.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-esh3k0/Debug/cmTC_f71f9.lib" /MACHINE:X64  /machine:x64 cmTC_f71f9.dir\\Debug\\src.obj
          cmTC_f71f9.vcxproj -> C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-esh3k0\\Debug\\cmTC_f71f9.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_f71f9.dir\\Debug\\cmTC_f71f9.tlog\\unsuccessfulbuild".
          Touching "cmTC_f71f9.dir\\Debug\\cmTC_f71f9.tlog\\cmTC_f71f9.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\hhhh\\Lorenzo_game\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-esh3k0\\cmTC_f71f9.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.78
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "find-v1"
    backtrace:
      - "ggml/src/CMakeLists.txt:383 (find_library)"
    mode: "library"
    variable: "MATH_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "m"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/lib/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/lib/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "/bin/"
    searched_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/lib/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/lib/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "/bin/"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "C:/Program Files (x86)/whisper.cpp/bin"
        - "C:/Program Files/CMake/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "ggml/CMakeLists.txt:300 (find_program)"
    mode: "program"
    variable: "GIT_EXE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
      - "git.exe"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Users/<USER>/bin/git.com"
      - "C:/Users/<USER>/bin/git.exe"
      - "C:/Users/<USER>/bin/git"
      - "C:/Program Files/Git/mingw64/bin/git.com"
    found: "C:/Program Files/Git/mingw64/bin/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:171 (find_program)"
      - "CMakeLists.txt:215 (include)"
    mode: "program"
    variable: "GITCOMMAND"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Users/<USER>/bin/git.com"
      - "C:/Users/<USER>/bin/git.exe"
      - "C:/Users/<USER>/bin/git"
      - "C:/Program Files/Git/mingw64/bin/git.com"
    found: "C:/Program Files/Git/mingw64/bin/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:188 (find_program)"
      - "CMakeLists.txt:215 (include)"
    mode: "program"
    variable: "MEMORYCHECK_COMMAND"
    description: "Path to the memory checking command, used for memory error detection."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "purify"
      - "valgrind"
      - "boundscheck"
      - "drmemory"
      - "cuda-memcheck"
      - "compute-sanitizer"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "/REGISTRY-NOTFOUND/"
    searched_directories:
      - "C:/Users/<USER>/bin/purify.com"
      - "C:/Users/<USER>/bin/purify.exe"
      - "C:/Users/<USER>/bin/purify"
      - "C:/Program Files/Git/mingw64/bin/purify.com"
      - "C:/Program Files/Git/mingw64/bin/purify.exe"
      - "C:/Program Files/Git/mingw64/bin/purify"
      - "C:/Program Files/Git/usr/local/bin/purify.com"
      - "C:/Program Files/Git/usr/local/bin/purify.exe"
      - "C:/Program Files/Git/usr/local/bin/purify"
      - "C:/Program Files/Git/usr/bin/purify.com"
      - "C:/Program Files/Git/usr/bin/purify.exe"
      - "C:/Program Files/Git/usr/bin/purify"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/purify.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/purify.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/purify"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/purify.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/purify.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/purify"
      - "C:/Windows/System32/purify.com"
      - "C:/Windows/System32/purify.exe"
      - "C:/Windows/System32/purify"
      - "C:/Windows/purify.com"
      - "C:/Windows/purify.exe"
      - "C:/Windows/purify"
      - "C:/Windows/System32/wbem/purify.com"
      - "C:/Windows/System32/wbem/purify.exe"
      - "C:/Windows/System32/wbem/purify"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify"
      - "C:/Windows/System32/OpenSSH/purify.com"
      - "C:/Windows/System32/OpenSSH/purify.exe"
      - "C:/Windows/System32/OpenSSH/purify"
      - "C:/Program Files/nodejs/purify.com"
      - "C:/Program Files/nodejs/purify.exe"
      - "C:/Program Files/nodejs/purify"
      - "C:/ProgramData/chocolatey/bin/purify.com"
      - "C:/ProgramData/chocolatey/bin/purify.exe"
      - "C:/ProgramData/chocolatey/bin/purify"
      - "C:/Program Files/Git/cmd/purify.com"
      - "C:/Program Files/Git/cmd/purify.exe"
      - "C:/Program Files/Git/cmd/purify"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/purify.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/purify.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/purify"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/purify.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/purify.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/purify"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/purify.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/purify.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/purify"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify"
      - "C:/Program Files/Git/usr/bin/vendor_perl/purify.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/purify.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/purify"
      - "C:/Program Files/Git/usr/bin/core_perl/purify.com"
      - "C:/Program Files/Git/usr/bin/core_perl/purify.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/purify"
      - "C:/Program Files/bin/purify.com"
      - "C:/Program Files/bin/purify.exe"
      - "C:/Program Files/bin/purify"
      - "C:/Program Files/sbin/purify.com"
      - "C:/Program Files/sbin/purify.exe"
      - "C:/Program Files/sbin/purify"
      - "C:/Program Files/purify.com"
      - "C:/Program Files/purify.exe"
      - "C:/Program Files/purify"
      - "C:/Program Files (x86)/bin/purify.com"
      - "C:/Program Files (x86)/bin/purify.exe"
      - "C:/Program Files (x86)/bin/purify"
      - "C:/Program Files (x86)/sbin/purify.com"
      - "C:/Program Files (x86)/sbin/purify.exe"
      - "C:/Program Files (x86)/sbin/purify"
      - "C:/Program Files (x86)/purify.com"
      - "C:/Program Files (x86)/purify.exe"
      - "C:/Program Files (x86)/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Program Files/CMake/sbin/purify.com"
      - "C:/Program Files/CMake/sbin/purify.exe"
      - "C:/Program Files/CMake/sbin/purify"
      - "C:/Program Files/CMake/purify.com"
      - "C:/Program Files/CMake/purify.exe"
      - "C:/Program Files/CMake/purify"
      - "C:/Program Files (x86)/whisper.cpp/bin/purify.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/purify.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/purify"
      - "C:/Program Files (x86)/whisper.cpp/sbin/purify.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/purify.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/purify"
      - "C:/Program Files (x86)/whisper.cpp/purify.com"
      - "C:/Program Files (x86)/whisper.cpp/purify.exe"
      - "C:/Program Files (x86)/whisper.cpp/purify"
      - "/REGISTRY-NOTFOUND/purify.com"
      - "/REGISTRY-NOTFOUND/purify.exe"
      - "/REGISTRY-NOTFOUND/purify"
      - "C:/Users/<USER>/bin/valgrind.com"
      - "C:/Users/<USER>/bin/valgrind.exe"
      - "C:/Users/<USER>/bin/valgrind"
      - "C:/Program Files/Git/mingw64/bin/valgrind.com"
      - "C:/Program Files/Git/mingw64/bin/valgrind.exe"
      - "C:/Program Files/Git/mingw64/bin/valgrind"
      - "C:/Program Files/Git/usr/local/bin/valgrind.com"
      - "C:/Program Files/Git/usr/local/bin/valgrind.exe"
      - "C:/Program Files/Git/usr/local/bin/valgrind"
      - "C:/Program Files/Git/usr/bin/valgrind.com"
      - "C:/Program Files/Git/usr/bin/valgrind.exe"
      - "C:/Program Files/Git/usr/bin/valgrind"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/valgrind.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/valgrind.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/valgrind"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/valgrind.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/valgrind.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/valgrind"
      - "C:/Windows/System32/valgrind.com"
      - "C:/Windows/System32/valgrind.exe"
      - "C:/Windows/System32/valgrind"
      - "C:/Windows/valgrind.com"
      - "C:/Windows/valgrind.exe"
      - "C:/Windows/valgrind"
      - "C:/Windows/System32/wbem/valgrind.com"
      - "C:/Windows/System32/wbem/valgrind.exe"
      - "C:/Windows/System32/wbem/valgrind"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind"
      - "C:/Windows/System32/OpenSSH/valgrind.com"
      - "C:/Windows/System32/OpenSSH/valgrind.exe"
      - "C:/Windows/System32/OpenSSH/valgrind"
      - "C:/Program Files/nodejs/valgrind.com"
      - "C:/Program Files/nodejs/valgrind.exe"
      - "C:/Program Files/nodejs/valgrind"
      - "C:/ProgramData/chocolatey/bin/valgrind.com"
      - "C:/ProgramData/chocolatey/bin/valgrind.exe"
      - "C:/ProgramData/chocolatey/bin/valgrind"
      - "C:/Program Files/Git/cmd/valgrind.com"
      - "C:/Program Files/Git/cmd/valgrind.exe"
      - "C:/Program Files/Git/cmd/valgrind"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/valgrind.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/valgrind.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/valgrind"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/valgrind.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/valgrind.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/valgrind"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/valgrind.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/valgrind.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/valgrind"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind"
      - "C:/Program Files/Git/usr/bin/vendor_perl/valgrind.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/valgrind.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/valgrind"
      - "C:/Program Files/Git/usr/bin/core_perl/valgrind.com"
      - "C:/Program Files/Git/usr/bin/core_perl/valgrind.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/valgrind"
      - "C:/Program Files/bin/valgrind.com"
      - "C:/Program Files/bin/valgrind.exe"
      - "C:/Program Files/bin/valgrind"
      - "C:/Program Files/sbin/valgrind.com"
      - "C:/Program Files/sbin/valgrind.exe"
      - "C:/Program Files/sbin/valgrind"
      - "C:/Program Files/valgrind.com"
      - "C:/Program Files/valgrind.exe"
      - "C:/Program Files/valgrind"
      - "C:/Program Files (x86)/bin/valgrind.com"
      - "C:/Program Files (x86)/bin/valgrind.exe"
      - "C:/Program Files (x86)/bin/valgrind"
      - "C:/Program Files (x86)/sbin/valgrind.com"
      - "C:/Program Files (x86)/sbin/valgrind.exe"
      - "C:/Program Files (x86)/sbin/valgrind"
      - "C:/Program Files (x86)/valgrind.com"
      - "C:/Program Files (x86)/valgrind.exe"
      - "C:/Program Files (x86)/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Program Files/CMake/sbin/valgrind.com"
      - "C:/Program Files/CMake/sbin/valgrind.exe"
      - "C:/Program Files/CMake/sbin/valgrind"
      - "C:/Program Files/CMake/valgrind.com"
      - "C:/Program Files/CMake/valgrind.exe"
      - "C:/Program Files/CMake/valgrind"
      - "C:/Program Files (x86)/whisper.cpp/bin/valgrind.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/valgrind.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/valgrind"
      - "C:/Program Files (x86)/whisper.cpp/sbin/valgrind.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/valgrind.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/valgrind"
      - "C:/Program Files (x86)/whisper.cpp/valgrind.com"
      - "C:/Program Files (x86)/whisper.cpp/valgrind.exe"
      - "C:/Program Files (x86)/whisper.cpp/valgrind"
      - "/REGISTRY-NOTFOUND/valgrind.com"
      - "/REGISTRY-NOTFOUND/valgrind.exe"
      - "/REGISTRY-NOTFOUND/valgrind"
      - "C:/Users/<USER>/bin/boundscheck.com"
      - "C:/Users/<USER>/bin/boundscheck.exe"
      - "C:/Users/<USER>/bin/boundscheck"
      - "C:/Program Files/Git/mingw64/bin/boundscheck.com"
      - "C:/Program Files/Git/mingw64/bin/boundscheck.exe"
      - "C:/Program Files/Git/mingw64/bin/boundscheck"
      - "C:/Program Files/Git/usr/local/bin/boundscheck.com"
      - "C:/Program Files/Git/usr/local/bin/boundscheck.exe"
      - "C:/Program Files/Git/usr/local/bin/boundscheck"
      - "C:/Program Files/Git/usr/bin/boundscheck.com"
      - "C:/Program Files/Git/usr/bin/boundscheck.exe"
      - "C:/Program Files/Git/usr/bin/boundscheck"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/boundscheck.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/boundscheck.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/boundscheck"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/boundscheck.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/boundscheck.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/boundscheck"
      - "C:/Windows/System32/boundscheck.com"
      - "C:/Windows/System32/boundscheck.exe"
      - "C:/Windows/System32/boundscheck"
      - "C:/Windows/boundscheck.com"
      - "C:/Windows/boundscheck.exe"
      - "C:/Windows/boundscheck"
      - "C:/Windows/System32/wbem/boundscheck.com"
      - "C:/Windows/System32/wbem/boundscheck.exe"
      - "C:/Windows/System32/wbem/boundscheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck"
      - "C:/Windows/System32/OpenSSH/boundscheck.com"
      - "C:/Windows/System32/OpenSSH/boundscheck.exe"
      - "C:/Windows/System32/OpenSSH/boundscheck"
      - "C:/Program Files/nodejs/boundscheck.com"
      - "C:/Program Files/nodejs/boundscheck.exe"
      - "C:/Program Files/nodejs/boundscheck"
      - "C:/ProgramData/chocolatey/bin/boundscheck.com"
      - "C:/ProgramData/chocolatey/bin/boundscheck.exe"
      - "C:/ProgramData/chocolatey/bin/boundscheck"
      - "C:/Program Files/Git/cmd/boundscheck.com"
      - "C:/Program Files/Git/cmd/boundscheck.exe"
      - "C:/Program Files/Git/cmd/boundscheck"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/boundscheck.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/boundscheck.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/boundscheck"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/boundscheck.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/boundscheck.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/boundscheck"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/boundscheck.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/boundscheck.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck"
      - "C:/Program Files/Git/usr/bin/vendor_perl/boundscheck.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/boundscheck.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/boundscheck"
      - "C:/Program Files/Git/usr/bin/core_perl/boundscheck.com"
      - "C:/Program Files/Git/usr/bin/core_perl/boundscheck.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/boundscheck"
      - "C:/Program Files/bin/boundscheck.com"
      - "C:/Program Files/bin/boundscheck.exe"
      - "C:/Program Files/bin/boundscheck"
      - "C:/Program Files/sbin/boundscheck.com"
      - "C:/Program Files/sbin/boundscheck.exe"
      - "C:/Program Files/sbin/boundscheck"
      - "C:/Program Files/boundscheck.com"
      - "C:/Program Files/boundscheck.exe"
      - "C:/Program Files/boundscheck"
      - "C:/Program Files (x86)/bin/boundscheck.com"
      - "C:/Program Files (x86)/bin/boundscheck.exe"
      - "C:/Program Files (x86)/bin/boundscheck"
      - "C:/Program Files (x86)/sbin/boundscheck.com"
      - "C:/Program Files (x86)/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/sbin/boundscheck"
      - "C:/Program Files (x86)/boundscheck.com"
      - "C:/Program Files (x86)/boundscheck.exe"
      - "C:/Program Files (x86)/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Program Files/CMake/sbin/boundscheck.com"
      - "C:/Program Files/CMake/sbin/boundscheck.exe"
      - "C:/Program Files/CMake/sbin/boundscheck"
      - "C:/Program Files/CMake/boundscheck.com"
      - "C:/Program Files/CMake/boundscheck.exe"
      - "C:/Program Files/CMake/boundscheck"
      - "C:/Program Files (x86)/whisper.cpp/bin/boundscheck.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/boundscheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/boundscheck"
      - "C:/Program Files (x86)/whisper.cpp/sbin/boundscheck.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/boundscheck"
      - "C:/Program Files (x86)/whisper.cpp/boundscheck.com"
      - "C:/Program Files (x86)/whisper.cpp/boundscheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/boundscheck"
      - "/REGISTRY-NOTFOUND/boundscheck.com"
      - "/REGISTRY-NOTFOUND/boundscheck.exe"
      - "/REGISTRY-NOTFOUND/boundscheck"
      - "C:/Users/<USER>/bin/drmemory.com"
      - "C:/Users/<USER>/bin/drmemory.exe"
      - "C:/Users/<USER>/bin/drmemory"
      - "C:/Program Files/Git/mingw64/bin/drmemory.com"
      - "C:/Program Files/Git/mingw64/bin/drmemory.exe"
      - "C:/Program Files/Git/mingw64/bin/drmemory"
      - "C:/Program Files/Git/usr/local/bin/drmemory.com"
      - "C:/Program Files/Git/usr/local/bin/drmemory.exe"
      - "C:/Program Files/Git/usr/local/bin/drmemory"
      - "C:/Program Files/Git/usr/bin/drmemory.com"
      - "C:/Program Files/Git/usr/bin/drmemory.exe"
      - "C:/Program Files/Git/usr/bin/drmemory"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/drmemory.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/drmemory.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/drmemory"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/drmemory.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/drmemory.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/drmemory"
      - "C:/Windows/System32/drmemory.com"
      - "C:/Windows/System32/drmemory.exe"
      - "C:/Windows/System32/drmemory"
      - "C:/Windows/drmemory.com"
      - "C:/Windows/drmemory.exe"
      - "C:/Windows/drmemory"
      - "C:/Windows/System32/wbem/drmemory.com"
      - "C:/Windows/System32/wbem/drmemory.exe"
      - "C:/Windows/System32/wbem/drmemory"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory"
      - "C:/Windows/System32/OpenSSH/drmemory.com"
      - "C:/Windows/System32/OpenSSH/drmemory.exe"
      - "C:/Windows/System32/OpenSSH/drmemory"
      - "C:/Program Files/nodejs/drmemory.com"
      - "C:/Program Files/nodejs/drmemory.exe"
      - "C:/Program Files/nodejs/drmemory"
      - "C:/ProgramData/chocolatey/bin/drmemory.com"
      - "C:/ProgramData/chocolatey/bin/drmemory.exe"
      - "C:/ProgramData/chocolatey/bin/drmemory"
      - "C:/Program Files/Git/cmd/drmemory.com"
      - "C:/Program Files/Git/cmd/drmemory.exe"
      - "C:/Program Files/Git/cmd/drmemory"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/drmemory.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/drmemory.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/drmemory"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/drmemory.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/drmemory.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/drmemory"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/drmemory.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/drmemory.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/drmemory"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory"
      - "C:/Program Files/Git/usr/bin/vendor_perl/drmemory.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/drmemory.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/drmemory"
      - "C:/Program Files/Git/usr/bin/core_perl/drmemory.com"
      - "C:/Program Files/Git/usr/bin/core_perl/drmemory.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/drmemory"
      - "C:/Program Files/bin/drmemory.com"
      - "C:/Program Files/bin/drmemory.exe"
      - "C:/Program Files/bin/drmemory"
      - "C:/Program Files/sbin/drmemory.com"
      - "C:/Program Files/sbin/drmemory.exe"
      - "C:/Program Files/sbin/drmemory"
      - "C:/Program Files/drmemory.com"
      - "C:/Program Files/drmemory.exe"
      - "C:/Program Files/drmemory"
      - "C:/Program Files (x86)/bin/drmemory.com"
      - "C:/Program Files (x86)/bin/drmemory.exe"
      - "C:/Program Files (x86)/bin/drmemory"
      - "C:/Program Files (x86)/sbin/drmemory.com"
      - "C:/Program Files (x86)/sbin/drmemory.exe"
      - "C:/Program Files (x86)/sbin/drmemory"
      - "C:/Program Files (x86)/drmemory.com"
      - "C:/Program Files (x86)/drmemory.exe"
      - "C:/Program Files (x86)/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Program Files/CMake/sbin/drmemory.com"
      - "C:/Program Files/CMake/sbin/drmemory.exe"
      - "C:/Program Files/CMake/sbin/drmemory"
      - "C:/Program Files/CMake/drmemory.com"
      - "C:/Program Files/CMake/drmemory.exe"
      - "C:/Program Files/CMake/drmemory"
      - "C:/Program Files (x86)/whisper.cpp/bin/drmemory.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/drmemory.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/drmemory"
      - "C:/Program Files (x86)/whisper.cpp/sbin/drmemory.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/drmemory.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/drmemory"
      - "C:/Program Files (x86)/whisper.cpp/drmemory.com"
      - "C:/Program Files (x86)/whisper.cpp/drmemory.exe"
      - "C:/Program Files (x86)/whisper.cpp/drmemory"
      - "/REGISTRY-NOTFOUND/drmemory.com"
      - "/REGISTRY-NOTFOUND/drmemory.exe"
      - "/REGISTRY-NOTFOUND/drmemory"
      - "C:/Users/<USER>/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/bin/cuda-memcheck"
      - "C:/Program Files/Git/mingw64/bin/cuda-memcheck.com"
      - "C:/Program Files/Git/mingw64/bin/cuda-memcheck.exe"
      - "C:/Program Files/Git/mingw64/bin/cuda-memcheck"
      - "C:/Program Files/Git/usr/local/bin/cuda-memcheck.com"
      - "C:/Program Files/Git/usr/local/bin/cuda-memcheck.exe"
      - "C:/Program Files/Git/usr/local/bin/cuda-memcheck"
      - "C:/Program Files/Git/usr/bin/cuda-memcheck.com"
      - "C:/Program Files/Git/usr/bin/cuda-memcheck.exe"
      - "C:/Program Files/Git/usr/bin/cuda-memcheck"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/cuda-memcheck.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/cuda-memcheck.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/cuda-memcheck"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/cuda-memcheck.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/cuda-memcheck.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/cuda-memcheck"
      - "C:/Windows/System32/cuda-memcheck.com"
      - "C:/Windows/System32/cuda-memcheck.exe"
      - "C:/Windows/System32/cuda-memcheck"
      - "C:/Windows/cuda-memcheck.com"
      - "C:/Windows/cuda-memcheck.exe"
      - "C:/Windows/cuda-memcheck"
      - "C:/Windows/System32/wbem/cuda-memcheck.com"
      - "C:/Windows/System32/wbem/cuda-memcheck.exe"
      - "C:/Windows/System32/wbem/cuda-memcheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.com"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.exe"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck"
      - "C:/Program Files/nodejs/cuda-memcheck.com"
      - "C:/Program Files/nodejs/cuda-memcheck.exe"
      - "C:/Program Files/nodejs/cuda-memcheck"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.com"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.exe"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck"
      - "C:/Program Files/Git/cmd/cuda-memcheck.com"
      - "C:/Program Files/Git/cmd/cuda-memcheck.exe"
      - "C:/Program Files/Git/cmd/cuda-memcheck"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/cuda-memcheck.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/cuda-memcheck.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/cuda-memcheck"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/cuda-memcheck.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/cuda-memcheck.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/cuda-memcheck"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cuda-memcheck.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cuda-memcheck.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck"
      - "C:/Program Files/Git/usr/bin/vendor_perl/cuda-memcheck.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/cuda-memcheck.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/cuda-memcheck"
      - "C:/Program Files/Git/usr/bin/core_perl/cuda-memcheck.com"
      - "C:/Program Files/Git/usr/bin/core_perl/cuda-memcheck.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/cuda-memcheck"
      - "C:/Program Files/bin/cuda-memcheck.com"
      - "C:/Program Files/bin/cuda-memcheck.exe"
      - "C:/Program Files/bin/cuda-memcheck"
      - "C:/Program Files/sbin/cuda-memcheck.com"
      - "C:/Program Files/sbin/cuda-memcheck.exe"
      - "C:/Program Files/sbin/cuda-memcheck"
      - "C:/Program Files/cuda-memcheck.com"
      - "C:/Program Files/cuda-memcheck.exe"
      - "C:/Program Files/cuda-memcheck"
      - "C:/Program Files (x86)/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/bin/cuda-memcheck"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/cuda-memcheck.com"
      - "C:/Program Files (x86)/cuda-memcheck.exe"
      - "C:/Program Files (x86)/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.com"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/sbin/cuda-memcheck"
      - "C:/Program Files/CMake/cuda-memcheck.com"
      - "C:/Program Files/CMake/cuda-memcheck.exe"
      - "C:/Program Files/CMake/cuda-memcheck"
      - "C:/Program Files (x86)/whisper.cpp/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/cuda-memcheck"
      - "C:/Program Files (x86)/whisper.cpp/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/whisper.cpp/cuda-memcheck.com"
      - "C:/Program Files (x86)/whisper.cpp/cuda-memcheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/cuda-memcheck"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.com"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.exe"
      - "/REGISTRY-NOTFOUND/cuda-memcheck"
      - "C:/Users/<USER>/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/bin/compute-sanitizer"
      - "C:/Program Files/Git/mingw64/bin/compute-sanitizer.com"
      - "C:/Program Files/Git/mingw64/bin/compute-sanitizer.exe"
      - "C:/Program Files/Git/mingw64/bin/compute-sanitizer"
      - "C:/Program Files/Git/usr/local/bin/compute-sanitizer.com"
      - "C:/Program Files/Git/usr/local/bin/compute-sanitizer.exe"
      - "C:/Program Files/Git/usr/local/bin/compute-sanitizer"
      - "C:/Program Files/Git/usr/bin/compute-sanitizer.com"
      - "C:/Program Files/Git/usr/bin/compute-sanitizer.exe"
      - "C:/Program Files/Git/usr/bin/compute-sanitizer"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/compute-sanitizer.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/compute-sanitizer.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/compute-sanitizer"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/compute-sanitizer.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/compute-sanitizer.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/compute-sanitizer"
      - "C:/Windows/System32/compute-sanitizer.com"
      - "C:/Windows/System32/compute-sanitizer.exe"
      - "C:/Windows/System32/compute-sanitizer"
      - "C:/Windows/compute-sanitizer.com"
      - "C:/Windows/compute-sanitizer.exe"
      - "C:/Windows/compute-sanitizer"
      - "C:/Windows/System32/wbem/compute-sanitizer.com"
      - "C:/Windows/System32/wbem/compute-sanitizer.exe"
      - "C:/Windows/System32/wbem/compute-sanitizer"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.com"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.exe"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer"
      - "C:/Program Files/nodejs/compute-sanitizer.com"
      - "C:/Program Files/nodejs/compute-sanitizer.exe"
      - "C:/Program Files/nodejs/compute-sanitizer"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.com"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.exe"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer"
      - "C:/Program Files/Git/cmd/compute-sanitizer.com"
      - "C:/Program Files/Git/cmd/compute-sanitizer.exe"
      - "C:/Program Files/Git/cmd/compute-sanitizer"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/compute-sanitizer.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/compute-sanitizer.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/compute-sanitizer"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/compute-sanitizer.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/compute-sanitizer.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/compute-sanitizer"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/compute-sanitizer.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/compute-sanitizer.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer"
      - "C:/Program Files/Git/usr/bin/vendor_perl/compute-sanitizer.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/compute-sanitizer.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/compute-sanitizer"
      - "C:/Program Files/Git/usr/bin/core_perl/compute-sanitizer.com"
      - "C:/Program Files/Git/usr/bin/core_perl/compute-sanitizer.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/compute-sanitizer"
      - "C:/Program Files/bin/compute-sanitizer.com"
      - "C:/Program Files/bin/compute-sanitizer.exe"
      - "C:/Program Files/bin/compute-sanitizer"
      - "C:/Program Files/sbin/compute-sanitizer.com"
      - "C:/Program Files/sbin/compute-sanitizer.exe"
      - "C:/Program Files/sbin/compute-sanitizer"
      - "C:/Program Files/compute-sanitizer.com"
      - "C:/Program Files/compute-sanitizer.exe"
      - "C:/Program Files/compute-sanitizer"
      - "C:/Program Files (x86)/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/bin/compute-sanitizer"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/compute-sanitizer.com"
      - "C:/Program Files (x86)/compute-sanitizer.exe"
      - "C:/Program Files (x86)/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.com"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/sbin/compute-sanitizer"
      - "C:/Program Files/CMake/compute-sanitizer.com"
      - "C:/Program Files/CMake/compute-sanitizer.exe"
      - "C:/Program Files/CMake/compute-sanitizer"
      - "C:/Program Files (x86)/whisper.cpp/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/compute-sanitizer"
      - "C:/Program Files (x86)/whisper.cpp/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/whisper.cpp/compute-sanitizer.com"
      - "C:/Program Files (x86)/whisper.cpp/compute-sanitizer.exe"
      - "C:/Program Files (x86)/whisper.cpp/compute-sanitizer"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.com"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.exe"
      - "/REGISTRY-NOTFOUND/compute-sanitizer"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:196 (find_program)"
      - "CMakeLists.txt:215 (include)"
    mode: "program"
    variable: "COVERAGE_COMMAND"
    description: "Path to the coverage program that CTest uses for performing coverage inspection"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcov"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Users/<USER>/bin/gcov.com"
      - "C:/Users/<USER>/bin/gcov.exe"
      - "C:/Users/<USER>/bin/gcov"
      - "C:/Program Files/Git/mingw64/bin/gcov.com"
      - "C:/Program Files/Git/mingw64/bin/gcov.exe"
      - "C:/Program Files/Git/mingw64/bin/gcov"
      - "C:/Program Files/Git/usr/local/bin/gcov.com"
      - "C:/Program Files/Git/usr/local/bin/gcov.exe"
      - "C:/Program Files/Git/usr/local/bin/gcov"
      - "C:/Program Files/Git/usr/bin/gcov.com"
      - "C:/Program Files/Git/usr/bin/gcov.exe"
      - "C:/Program Files/Git/usr/bin/gcov"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/gcov.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/gcov.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/gcov"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/gcov.com"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/gcov.exe"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/libnvvp/gcov"
      - "C:/Windows/System32/gcov.com"
      - "C:/Windows/System32/gcov.exe"
      - "C:/Windows/System32/gcov"
      - "C:/Windows/gcov.com"
      - "C:/Windows/gcov.exe"
      - "C:/Windows/gcov"
      - "C:/Windows/System32/wbem/gcov.com"
      - "C:/Windows/System32/wbem/gcov.exe"
      - "C:/Windows/System32/wbem/gcov"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov"
      - "C:/Windows/System32/OpenSSH/gcov.com"
      - "C:/Windows/System32/OpenSSH/gcov.exe"
      - "C:/Windows/System32/OpenSSH/gcov"
      - "C:/Program Files/nodejs/gcov.com"
      - "C:/Program Files/nodejs/gcov.exe"
      - "C:/Program Files/nodejs/gcov"
      - "C:/ProgramData/chocolatey/bin/gcov.com"
      - "C:/ProgramData/chocolatey/bin/gcov.exe"
      - "C:/ProgramData/chocolatey/bin/gcov"
      - "C:/Program Files/Git/cmd/gcov.com"
      - "C:/Program Files/Git/cmd/gcov.exe"
      - "C:/Program Files/Git/cmd/gcov"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/gcov.com"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/gcov.exe"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2025.2.1/gcov"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/gcov.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/gcov.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA App/NvDLISR/gcov"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcov.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcov.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code Insiders/bin/gcov"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov"
      - "C:/Program Files/Git/usr/bin/vendor_perl/gcov.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/gcov.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/gcov"
      - "C:/Program Files/Git/usr/bin/core_perl/gcov.com"
      - "C:/Program Files/Git/usr/bin/core_perl/gcov.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/gcov"
      - "C:/Program Files/bin/gcov.com"
      - "C:/Program Files/bin/gcov.exe"
      - "C:/Program Files/bin/gcov"
      - "C:/Program Files/sbin/gcov.com"
      - "C:/Program Files/sbin/gcov.exe"
      - "C:/Program Files/sbin/gcov"
      - "C:/Program Files/gcov.com"
      - "C:/Program Files/gcov.exe"
      - "C:/Program Files/gcov"
      - "C:/Program Files (x86)/bin/gcov.com"
      - "C:/Program Files (x86)/bin/gcov.exe"
      - "C:/Program Files (x86)/bin/gcov"
      - "C:/Program Files (x86)/sbin/gcov.com"
      - "C:/Program Files (x86)/sbin/gcov.exe"
      - "C:/Program Files (x86)/sbin/gcov"
      - "C:/Program Files (x86)/gcov.com"
      - "C:/Program Files (x86)/gcov.exe"
      - "C:/Program Files (x86)/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Program Files/CMake/sbin/gcov.com"
      - "C:/Program Files/CMake/sbin/gcov.exe"
      - "C:/Program Files/CMake/sbin/gcov"
      - "C:/Program Files/CMake/gcov.com"
      - "C:/Program Files/CMake/gcov.exe"
      - "C:/Program Files/CMake/gcov"
      - "C:/Program Files (x86)/whisper.cpp/bin/gcov.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/gcov.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/gcov"
      - "C:/Program Files (x86)/whisper.cpp/sbin/gcov.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/gcov.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/gcov"
      - "C:/Program Files (x86)/whisper.cpp/gcov.com"
      - "C:/Program Files (x86)/whisper.cpp/gcov.exe"
      - "C:/Program Files (x86)/whisper.cpp/gcov"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files\\nodejs"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.1"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
...
