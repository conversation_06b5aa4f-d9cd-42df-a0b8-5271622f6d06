﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1141DA7B-ADB9-3983-974B-970F38AA644D}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>whisper</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">whisper.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">whisper</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">whisper.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">whisper</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">whisper.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">whisper</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">whisper.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">whisper</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION="1.7.6";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR="Debug";whisper_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION=\"1.7.6\";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR=\"Debug\";whisper_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/Debug/exports.def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/Debug//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ggml\src\Debug\ggml.lib;..\ggml\src\Debug\ggml-cpu.lib;..\ggml\src\Debug\ggml-base.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/Debug/whisper.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ModuleDefinitionFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/Debug/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem></SubSystem>
      <Version>1.7</Version>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION="1.7.6";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR="Release";whisper_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION=\"1.7.6\";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR=\"Release\";whisper_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/Release/exports.def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/Release//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ggml\src\Release\ggml.lib;..\ggml\src\Release\ggml-cpu.lib;..\ggml\src\Release\ggml-base.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/Release/whisper.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ModuleDefinitionFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/Release/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem></SubSystem>
      <Version>1.7</Version>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION="1.7.6";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR="MinSizeRel";whisper_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION=\"1.7.6\";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR=\"MinSizeRel\";whisper_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/MinSizeRel/exports.def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/MinSizeRel//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ggml\src\MinSizeRel\ggml.lib;..\ggml\src\MinSizeRel\ggml-cpu.lib;..\ggml\src\MinSizeRel\ggml-base.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/MinSizeRel/whisper.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ModuleDefinitionFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/MinSizeRel/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem></SubSystem>
      <Version>1.7</Version>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION="1.7.6";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR="RelWithDebInfo";whisper_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;WHISPER_SHARED;WHISPER_BUILD;WHISPER_VERSION=\"1.7.6\";_CRT_SECURE_NO_WARNINGS;GGML_USE_CPU;GGML_SHARED;GGML_BACKEND_SHARED;CMAKE_INTDIR=\"RelWithDebInfo\";whisper_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\.;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\..\include;C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\ggml\src\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/RelWithDebInfo/exports.def C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/RelWithDebInfo//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>..\ggml\src\RelWithDebInfo\ggml.lib;..\ggml\src\RelWithDebInfo\ggml-cpu.lib;..\ggml\src\RelWithDebInfo\ggml-base.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <DataExecutionPrevention></DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImageHasSafeExceptionHandlers></ImageHasSafeExceptionHandlers>
      <ImportLibrary>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/RelWithDebInfo/whisper.lib</ImportLibrary>
      <LinkErrorReporting></LinkErrorReporting>
      <ModuleDefinitionFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/whisper.dir/RelWithDebInfo/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper.pdb</ProgramDataBaseFile>
      <RandomizedBaseAddress></RandomizedBaseAddress>
      <SubSystem></SubSystem>
      <Version>1.7</Version>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp -BC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build --check-stamp-file C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp -BC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build --check-stamp-file C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp -BC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build --check-stamp-file C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp -BC:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build --check-stamp-file C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\include\whisper.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\whisper-arch.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\src\whisper.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\ZERO_CHECK.vcxproj">
      <Project>{329087FE-BA1C-36BD-A96C-7843776A63C5}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\ggml\src\ggml.vcxproj">
      <Project>{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}</Project>
      <Name>ggml</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\ggml\src\ggml-base.vcxproj">
      <Project>{697CAFA6-0EE1-3F69-AF49-3C5303F50007}</Project>
      <Name>ggml-base</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\hhhh\Lorenzo_game\whisper.cpp\build\ggml\src\ggml-cpu.vcxproj">
      <Project>{54D2F21E-E179-3772-B611-9EDF7E8CA92F}</Project>
      <Name>ggml-cpu</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>