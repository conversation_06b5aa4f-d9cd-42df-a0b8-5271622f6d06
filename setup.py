#!/usr/bin/env python3
"""
Setup script for Audio Transcriber
Installs required dependencies for the pygame audio transcription interface
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    try:
        # Install pygame
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pygame"])
        print("✓ pygame installed successfully")
        
        # Install pyaudio (might need special handling on Windows)
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyaudio"])
            print("✓ pyaudio installed successfully")
        except subprocess.CalledProcessError:
            print("⚠ pyaudio installation failed. You may need to install it manually.")
            print("On Windows, try: pip install pipwin && pipwin install pyaudio")
            print("Or download the wheel from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
            
    except subprocess.CalledProcessError as e:
        print(f"Error installing packages: {e}")
        return False
        
    return True

def check_whisper_cpp():
    """Check if whisper.cpp is built and ready"""
    whisper_path = os.path.join("whisper.cpp", "build", "bin", "Release", "whisper-cli.exe")
    
    if os.path.exists(whisper_path):
        print("✓ whisper.cpp executable found")
        return True
    else:
        print("⚠ whisper.cpp executable not found")
        print("Make sure you've built whisper.cpp with:")
        print("  cd whisper.cpp")
        print("  cmake -B build")
        print("  cmake --build build -j --config Release")
        return False

def main():
    print("Setting up Audio Transcriber...")
    print("=" * 40)
    
    # Install Python dependencies
    if not install_requirements():
        print("Failed to install some dependencies")
        return 1
        
    print()
    
    # Check whisper.cpp
    if not check_whisper_cpp():
        print("whisper.cpp not ready")
        return 1
        
    print()
    print("✓ Setup complete!")
    print("Run the transcriber with: python audio_transcriber.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
