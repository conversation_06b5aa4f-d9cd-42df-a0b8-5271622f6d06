#version 450

#include "common.comp"

layout(local_size_x = 1) in;

layout(binding = 0) buffer restrict readonly tensorIn { float in_[]; };
layout(binding = 1) buffer restrict writeonly tensorOut { float out_[]; };

layout(push_constant) uniform PushConstants {
    uint inOff;
    uint outOff;
    float scale;
} pcs;

void main() {
    const uint i = gl_WorkGroupID.x;
    out_[i + pcs.outOff] = in_[i + pcs.inOff] * pcs.scale;
}
