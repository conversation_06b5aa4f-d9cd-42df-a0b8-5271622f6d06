# Audio Transcriber with Whisper.cpp

A simple pygame-based interface for recording audio and transcribing it using whisper.cpp.

## Features

- Real-time audio recording with visual feedback
- One-click transcription using whisper.cpp
- Simple, intuitive pygame interface
- Automatic audio file management
- Support for 16kHz audio (optimal for Whisper)

## Prerequisites

1. **whisper.cpp** must be built and ready:
   ```bash
   cd whisper.cpp
   cmake -B build
   cmake --build build -j --config Release
   ```

2. **Python 3.7+** with pip

## Installation

1. Run the setup script to install dependencies:
   ```bash
   python setup.py
   ```

   Or install manually:
   ```bash
   pip install pygame pyaudio
   ```

   **Note for Windows users**: If pyaudio installation fails, try:
   ```bash
   pip install pipwin
   pipwin install pyaudio
   ```

## Usage

1. Start the application:
   ```bash
   python audio_transcriber.py
   ```

2. Use the interface:
   - **Start Recording**: Begin recording audio from your microphone
   - **Stop Recording**: Stop recording and save the audio file
   - **Transcribe**: Process the recorded audio with whisper.cpp

## Interface

The application window contains:
- Three main buttons for recording and transcription
- Status display showing current operation
- Large text area displaying transcription results
- Visual feedback during recording (red button)

## Audio Settings

- **Sample Rate**: 16kHz (optimal for Whisper)
- **Channels**: Mono
- **Format**: 16-bit PCM
- **File Format**: WAV

## Troubleshooting

### PyAudio Installation Issues
- On Windows: Use `pipwin install pyaudio` or download from [here](https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio)
- On macOS: `brew install portaudio` then `pip install pyaudio`
- On Linux: `sudo apt-get install portaudio19-dev` then `pip install pyaudio`

### Whisper.cpp Not Found
- Ensure whisper.cpp is built in the `whisper.cpp` directory
- Check that `whisper.cpp/build/bin/Release/whisper-cli.exe` exists
- Make sure you have a model file (e.g., `ggml-base.en.bin`) in `whisper.cpp/models/`

### No Audio Input
- Check microphone permissions
- Ensure your microphone is working in other applications
- Try running as administrator (Windows) if needed

## File Structure

```
Lorenzo_game/
├── audio_transcriber.py    # Main application
├── setup.py               # Setup script
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── whisper.cpp/          # whisper.cpp repository
    └── build/bin/Release/
        └── whisper-cli.exe
```

## Dependencies

- **pygame**: GUI framework
- **pyaudio**: Audio recording
- **wave**: Audio file handling
- **subprocess**: Running whisper.cpp
- **threading**: Non-blocking operations

## License

This project uses whisper.cpp which is licensed under the MIT License.
