import pygame
import pyaudio
import wave
import threading
import subprocess
import os
import sys
from datetime import datetime

# Initialize pygame
pygame.init()

# Constants
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
BACKGROUND_COLOR = (30, 30, 40)
BUTTON_COLOR = (70, 130, 180)
BUTTON_HOVER_COLOR = (100, 149, 237)
BUTTON_RECORDING_COLOR = (220, 20, 60)
TEXT_COLOR = (255, 255, 255)
TRANSCRIPTION_COLOR = (200, 255, 200)

# Audio settings
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000  # Whisper works best with 16kHz

class AudioTranscriber:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("Audio Transcriber with Whisper.cpp")
        
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)
        
        self.is_recording = False
        self.audio_frames = []
        self.audio_stream = None
        self.pyaudio_instance = pyaudio.PyAudio()
        
        self.transcription_text = "Click 'Start Recording' to begin..."
        self.status_text = "Ready"
        
        # Button rectangles
        self.record_button = pygame.Rect(50, 50, 150, 60)
        self.stop_button = pygame.Rect(220, 50, 150, 60)
        self.transcribe_button = pygame.Rect(390, 50, 150, 60)

        # Language selection buttons
        self.auto_lang_button = pygame.Rect(560, 50, 80, 30)
        self.italian_button = pygame.Rect(560, 90, 80, 30)
        self.english_button = pygame.Rect(650, 90, 80, 30)

        # Selected language
        self.selected_language = "auto"  # auto, it, en
        
        self.recording_thread = None
        self.last_audio_file = None

        # Check for available models
        self._check_models()

    def _check_models(self):
        """Check for available whisper models and update status"""
        # Try multilingual models in order of preference
        model_candidates = [
            ("ggml-base.bin", "Base multilingual model"),
            ("ggml-tiny.bin", "Tiny multilingual model"),
            ("for-tests-ggml-base.bin", "Test base multilingual model"),
            ("ggml-base.en.bin", "Base English-only model")
        ]

        whisper_path = os.path.join("whisper.cpp", "build", "bin", "Release", "whisper-cli.exe")

        if not os.path.exists(whisper_path):
            self.status_text = "Whisper executable not found"
            self.transcription_text = "Please build whisper.cpp first"
            return

        # Find the best available model
        self.model_path = None
        self.model_name = None

        for model_file, model_desc in model_candidates:
            model_path = os.path.join("whisper.cpp", "models", model_file)
            if os.path.exists(model_path):
                self.model_path = model_path
                self.model_name = model_desc
                break

        if self.model_path:
            self.status_text = f"Ready - Using {self.model_name}"
            self.transcription_text = "Click 'Start Recording' to begin...\nSupports Italian and English"
        else:
            self.status_text = "No model files found"
            self.transcription_text = "Please download a whisper model file"

    def start_recording(self):
        if self.is_recording:
            return
            
        self.is_recording = True
        self.audio_frames = []
        self.status_text = "Recording..."
        self.transcription_text = "Recording in progress..."
        
        # Start recording in a separate thread
        self.recording_thread = threading.Thread(target=self._record_audio)
        self.recording_thread.start()
        
    def stop_recording(self):
        if not self.is_recording:
            return
            
        self.is_recording = False
        self.status_text = "Saving audio..."
        
        # Wait for recording thread to finish
        if self.recording_thread:
            self.recording_thread.join()
            
        # Save the recorded audio
        self._save_audio()
        self.status_text = "Audio saved. Click 'Transcribe' to process."
        
    def _record_audio(self):
        try:
            self.audio_stream = self.pyaudio_instance.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK
            )
            
            while self.is_recording:
                data = self.audio_stream.read(CHUNK)
                self.audio_frames.append(data)
                
        except Exception as e:
            self.status_text = f"Recording error: {str(e)}"
        finally:
            if self.audio_stream:
                self.audio_stream.stop_stream()
                self.audio_stream.close()
                
    def _save_audio(self):
        if not self.audio_frames:
            return
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.last_audio_file = f"recording_{timestamp}.wav"
        
        try:
            with wave.open(self.last_audio_file, 'wb') as wf:
                wf.setnchannels(CHANNELS)
                wf.setsampwidth(self.pyaudio_instance.get_sample_size(FORMAT))
                wf.setframerate(RATE)
                wf.writeframes(b''.join(self.audio_frames))
        except Exception as e:
            self.status_text = f"Save error: {str(e)}"
            
    def transcribe_audio(self):
        if not self.last_audio_file or not os.path.exists(self.last_audio_file):
            self.status_text = "No audio file to transcribe"
            return
            
        self.status_text = "Transcribing..."
        self.transcription_text = "Processing with Whisper.cpp..."
        
        # Run transcription in a separate thread
        transcription_thread = threading.Thread(target=self._run_transcription)
        transcription_thread.start()
        
    def _run_transcription(self):
        try:
            # Path to whisper-cli executable
            whisper_path = os.path.join("whisper.cpp", "build", "bin", "Release", "whisper-cli.exe")

            if not os.path.exists(whisper_path):
                self.status_text = "Whisper executable not found"
                return

            if not hasattr(self, 'model_path') or not self.model_path or not os.path.exists(self.model_path):
                self.status_text = "Model file not found"
                return

            # Run whisper transcription with explicit model path and language selection
            cmd = [
                whisper_path,
                "-f", self.last_audio_file,
                "-m", self.model_path,
                "-l", self.selected_language,  # Use selected language (auto, it, en)
                "-nt"  # No timestamps
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")

            if result.returncode == 0:
                # Extract transcription from output
                lines = result.stdout.strip().split('\n')
                transcription_lines = []
                detected_language = "Unknown"

                for line in lines:
                    # Check for language detection info
                    if "detected language:" in line.lower():
                        detected_language = line.split(":")[-1].strip()

                    # Skip system info and timing lines
                    if not any(skip in line.lower() for skip in ['whisper_', 'system_info', 'main:', 'load time', 'total time', 'detected language']):
                        if line.strip() and not line.startswith('[') and not line.startswith('whisper_print_timings'):
                            transcription_lines.append(line.strip())

                if transcription_lines:
                    transcription = ' '.join(transcription_lines)
                    self.transcription_text = f"Language: {detected_language}\n\n{transcription}"
                else:
                    self.transcription_text = "No speech detected"

                self.status_text = f"Transcription complete - {detected_language}"
            else:
                self.status_text = f"Transcription failed: {result.stderr}"
                self.transcription_text = f"Error details: {result.stderr}"

        except Exception as e:
            self.status_text = f"Error: {str(e)}"
            self.transcription_text = f"Transcription error: {str(e)}"
            
    def draw_button(self, rect, text, color, hover=False):
        button_color = color
        if hover:
            button_color = BUTTON_HOVER_COLOR
        if text == "Start Recording" and self.is_recording:
            button_color = BUTTON_RECORDING_COLOR
            
        pygame.draw.rect(self.screen, button_color, rect)
        pygame.draw.rect(self.screen, TEXT_COLOR, rect, 2)
        
        text_surface = self.font_medium.render(text, True, TEXT_COLOR)
        text_rect = text_surface.get_rect(center=rect.center)
        self.screen.blit(text_surface, text_rect)
        
    def draw_text_wrapped(self, text, rect, font, color):
        words = text.split(' ')
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            if font.size(test_line)[0] <= rect.width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    lines.append(word)
                    
        if current_line:
            lines.append(' '.join(current_line))
            
        y_offset = rect.top
        for line in lines:
            if y_offset + font.get_height() > rect.bottom:
                break
            text_surface = font.render(line, True, color)
            self.screen.blit(text_surface, (rect.left, y_offset))
            y_offset += font.get_height() + 5
            
    def run(self):
        clock = pygame.time.Clock()
        running = True
        
        while running:
            mouse_pos = pygame.mouse.get_pos()
            
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                    
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if self.record_button.collidepoint(mouse_pos):
                        if not self.is_recording:
                            self.start_recording()
                    elif self.stop_button.collidepoint(mouse_pos):
                        if self.is_recording:
                            self.stop_recording()
                    elif self.transcribe_button.collidepoint(mouse_pos):
                        if not self.is_recording:
                            self.transcribe_audio()
                    elif self.auto_lang_button.collidepoint(mouse_pos):
                        self.selected_language = "auto"
                    elif self.italian_button.collidepoint(mouse_pos):
                        self.selected_language = "it"
                    elif self.english_button.collidepoint(mouse_pos):
                        self.selected_language = "en"
                            
            # Clear screen
            self.screen.fill(BACKGROUND_COLOR)
            
            # Draw main buttons
            self.draw_button(self.record_button, "Record", BUTTON_COLOR,
                           self.record_button.collidepoint(mouse_pos))
            self.draw_button(self.stop_button, "Stop", BUTTON_COLOR,
                           self.stop_button.collidepoint(mouse_pos))
            self.draw_button(self.transcribe_button, "Transcribe", BUTTON_COLOR,
                           self.transcribe_button.collidepoint(mouse_pos))

            # Draw language selection buttons
            auto_color = BUTTON_HOVER_COLOR if self.selected_language == "auto" else BUTTON_COLOR
            it_color = BUTTON_HOVER_COLOR if self.selected_language == "it" else BUTTON_COLOR
            en_color = BUTTON_HOVER_COLOR if self.selected_language == "en" else BUTTON_COLOR

            self.draw_small_button(self.auto_lang_button, "Auto", auto_color)
            self.draw_small_button(self.italian_button, "IT", it_color)
            self.draw_small_button(self.english_button, "EN", en_color)

            # Language selection label
            lang_label = self.font_small.render("Language:", True, TEXT_COLOR)
            self.screen.blit(lang_label, (560, 30))
            
            # Draw status
            status_surface = self.font_medium.render(f"Status: {self.status_text}", True, TEXT_COLOR)
            self.screen.blit(status_surface, (50, 130))
            
            # Draw transcription area
            transcription_rect = pygame.Rect(50, 180, WINDOW_WIDTH - 100, WINDOW_HEIGHT - 230)
            pygame.draw.rect(self.screen, (50, 50, 60), transcription_rect)
            pygame.draw.rect(self.screen, TEXT_COLOR, transcription_rect, 2)
            
            # Title for transcription area
            title_surface = self.font_medium.render("Transcription:", True, TEXT_COLOR)
            self.screen.blit(title_surface, (60, 160))
            
            # Draw transcription text
            text_rect = pygame.Rect(60, 190, WINDOW_WIDTH - 120, WINDOW_HEIGHT - 250)
            self.draw_text_wrapped(self.transcription_text, text_rect, self.font_small, TRANSCRIPTION_COLOR)
            
            pygame.display.flip()
            clock.tick(60)
            
        # Cleanup
        if self.is_recording:
            self.stop_recording()
        self.pyaudio_instance.terminate()
        pygame.quit()

if __name__ == "__main__":
    try:
        app = AudioTranscriber()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)
