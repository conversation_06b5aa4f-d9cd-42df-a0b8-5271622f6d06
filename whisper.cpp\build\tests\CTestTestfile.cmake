# CMake generated Testfile for 
# Source directory: C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests
# Build directory: C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-tiny "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny PROPERTIES  LABELS "tiny;gh" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;19;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-tiny "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny PROPERTIES  LABELS "tiny;gh" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;19;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-tiny "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny PROPERTIES  LABELS "tiny;gh" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;19;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-tiny "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny PROPERTIES  LABELS "tiny;gh" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;19;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-tiny NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-tiny.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny.en PROPERTIES  LABELS "tiny;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;26;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-tiny.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny.en PROPERTIES  LABELS "tiny;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;26;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-tiny.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny.en PROPERTIES  LABELS "tiny;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;26;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-tiny.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-tiny.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-tiny.en PROPERTIES  LABELS "tiny;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;26;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-tiny.en NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-base "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base PROPERTIES  LABELS "base" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;33;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-base "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base PROPERTIES  LABELS "base" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;33;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-base "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base PROPERTIES  LABELS "base" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;33;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-base "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base PROPERTIES  LABELS "base" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;33;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-base NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-base.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base.en PROPERTIES  LABELS "base;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;40;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-base.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base.en PROPERTIES  LABELS "base;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;40;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-base.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base.en PROPERTIES  LABELS "base;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;40;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-base.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-base.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-base.en PROPERTIES  LABELS "base;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;40;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-base.en NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-small "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small PROPERTIES  LABELS "small" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;47;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-small "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small PROPERTIES  LABELS "small" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;47;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-small "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small PROPERTIES  LABELS "small" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;47;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-small "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small PROPERTIES  LABELS "small" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;47;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-small NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-small.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small.en PROPERTIES  LABELS "small;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-small.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small.en PROPERTIES  LABELS "small;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-small.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small.en PROPERTIES  LABELS "small;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-small.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-small.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-small.en PROPERTIES  LABELS "small;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-small.en NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-medium "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium PROPERTIES  LABELS "medium" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;61;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-medium "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium PROPERTIES  LABELS "medium" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;61;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-medium "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium PROPERTIES  LABELS "medium" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;61;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-medium "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.bin" "-l" "fr" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium PROPERTIES  LABELS "medium" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;61;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-medium NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-medium.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium.en PROPERTIES  LABELS "medium;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;68;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-medium.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium.en PROPERTIES  LABELS "medium;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;68;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-medium.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium.en PROPERTIES  LABELS "medium;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;68;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-medium.en "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-medium.en.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-medium.en PROPERTIES  LABELS "medium;en" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;68;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-medium.en NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-whisper-cli-large "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-large.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-large PROPERTIES  LABELS "large" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;75;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-whisper-cli-large "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-large.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-large PROPERTIES  LABELS "large" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;75;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-whisper-cli-large "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-large.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-large PROPERTIES  LABELS "large" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;75;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-whisper-cli-large "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/whisper-cli.exe" "-m" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/models/for-tests-ggml-large.bin" "-f" "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/samples/jfk.wav")
  set_tests_properties(test-whisper-cli-large PROPERTIES  LABELS "large" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;75;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-whisper-cli-large NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-vad "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/test-vad.exe")
  set_tests_properties(test-vad PROPERTIES  LABELS "unit" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;96;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-vad "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/test-vad.exe")
  set_tests_properties(test-vad PROPERTIES  LABELS "unit" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;96;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-vad "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/test-vad.exe")
  set_tests_properties(test-vad PROPERTIES  LABELS "unit" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;96;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-vad "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/test-vad.exe")
  set_tests_properties(test-vad PROPERTIES  LABELS "unit" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;96;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-vad NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test-vad-full "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Debug/test-vad-full.exe")
  set_tests_properties(test-vad-full PROPERTIES  _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;104;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test-vad-full "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/Release/test-vad-full.exe")
  set_tests_properties(test-vad-full PROPERTIES  _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;104;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test-vad-full "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/MinSizeRel/test-vad-full.exe")
  set_tests_properties(test-vad-full PROPERTIES  _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;104;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test-vad-full "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/build/bin/RelWithDebInfo/test-vad-full.exe")
  set_tests_properties(test-vad-full PROPERTIES  _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;104;add_test;C:/Users/<USER>/Desktop/hhhh/Lorenzo_game/whisper.cpp/tests/CMakeLists.txt;0;")
else()
  add_test(test-vad-full NOT_AVAILABLE)
endif()
