﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{EC61222C-DAA4-3802-8E5A-5475F8210641}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019} = {4DC1AD6B-0D2A-3743-B627-7740DB47A019}
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914} = {4429CFB4-02BF-3D4F-AAA8-185A51F06914}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77} = {D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}
		{0409B617-06D3-339E-A38B-0E8D55F28B4C} = {0409B617-06D3-339E-A38B-0E8D55F28B4C}
		{D42F0413-A84A-3B32-A23B-381224DF60E7} = {D42F0413-A84A-3B32-A23B-381224DF60E7}
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E} = {A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D} = {E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
		{60C3709A-B46C-313C-B088-C8095C05A0B1} = {60C3709A-B46C-313C-B088-C8095C05A0B1}
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA} = {3C972E0E-A360-33C6-9117-AD2273D6A3AA}
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E} = {E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Continuous", "Continuous.vcxproj", "{F98670D1-808D-33F2-8739-CFE71E8491A7}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Experimental", "Experimental.vcxproj", "{FF9090F1-4425-3248-B258-E19324C00F7A}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{2FBDCE20-3095-380B-92BE-2CEB3B591A77}"
	ProjectSection(ProjectDependencies) = postProject
		{EC61222C-DAA4-3802-8E5A-5475F8210641} = {EC61222C-DAA4-3802-8E5A-5475F8210641}
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Nightly", "Nightly.vcxproj", "{BF1FB228-6733-3D74-8A4C-D5E38567853B}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "NightlyMemoryCheck", "NightlyMemoryCheck.vcxproj", "{C2DC95EE-6BB9-3BFD-9E47-29E115B14C43}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{1350ADEA-93CA-329F-A31E-6E0BA4C61381}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{329087FE-BA1C-36BD-A96C-7843776A63C5}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "bench", "examples\deprecation-warning\bench.vcxproj", "{4DC1AD6B-0D2A-3743-B627-7740DB47A019}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "common", "examples\common.vcxproj", "{4429CFB4-02BF-3D4F-AAA8-185A51F06914}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml", "ggml\src\ggml.vcxproj", "{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml-base", "ggml\src\ggml-base.vcxproj", "{697CAFA6-0EE1-3F69-AF49-3C5303F50007}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml-cpu", "ggml\src\ggml-cpu.vcxproj", "{54D2F21E-E179-3772-B611-9EDF7E8CA92F}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "main", "examples\deprecation-warning\main.vcxproj", "{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "quantize", "examples\quantize\quantize.vcxproj", "{0409B617-06D3-339E-A38B-0E8D55F28B4C}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914} = {4429CFB4-02BF-3D4F-AAA8-185A51F06914}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test-vad", "tests\test-vad.vcxproj", "{D42F0413-A84A-3B32-A23B-381224DF60E7}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914} = {4429CFB4-02BF-3D4F-AAA8-185A51F06914}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test-vad-full", "tests\test-vad-full.vcxproj", "{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914} = {4429CFB4-02BF-3D4F-AAA8-185A51F06914}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "vad-speech-segments", "examples\vad-speech-segments\vad-speech-segments.vcxproj", "{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914} = {4429CFB4-02BF-3D4F-AAA8-185A51F06914}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "whisper", "src\whisper.vcxproj", "{1141DA7B-ADB9-3983-974B-970F38AA644D}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "whisper-bench", "examples\bench\whisper-bench.vcxproj", "{60C3709A-B46C-313C-B088-C8095C05A0B1}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "whisper-cli", "examples\cli\whisper-cli.vcxproj", "{3C972E0E-A360-33C6-9117-AD2273D6A3AA}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914} = {4429CFB4-02BF-3D4F-AAA8-185A51F06914}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "whisper-server", "examples\server\whisper-server.vcxproj", "{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}"
	ProjectSection(ProjectDependencies) = postProject
		{329087FE-BA1C-36BD-A96C-7843776A63C5} = {329087FE-BA1C-36BD-A96C-7843776A63C5}
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914} = {4429CFB4-02BF-3D4F-AAA8-185A51F06914}
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D} = {80C2E3E7-E328-3F5B-913B-74CE23B4B46D}
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007} = {697CAFA6-0EE1-3F69-AF49-3C5303F50007}
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F} = {54D2F21E-E179-3772-B611-9EDF7E8CA92F}
		{1141DA7B-ADB9-3983-974B-970F38AA644D} = {1141DA7B-ADB9-3983-974B-970F38AA644D}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Debug|x64.ActiveCfg = Debug|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Debug|x64.Build.0 = Debug|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Release|x64.ActiveCfg = Release|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.Release|x64.Build.0 = Release|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EC61222C-DAA4-3802-8E5A-5475F8210641}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F98670D1-808D-33F2-8739-CFE71E8491A7}.Debug|x64.ActiveCfg = Debug|x64
		{F98670D1-808D-33F2-8739-CFE71E8491A7}.Release|x64.ActiveCfg = Release|x64
		{F98670D1-808D-33F2-8739-CFE71E8491A7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F98670D1-808D-33F2-8739-CFE71E8491A7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FF9090F1-4425-3248-B258-E19324C00F7A}.Debug|x64.ActiveCfg = Debug|x64
		{FF9090F1-4425-3248-B258-E19324C00F7A}.Release|x64.ActiveCfg = Release|x64
		{FF9090F1-4425-3248-B258-E19324C00F7A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FF9090F1-4425-3248-B258-E19324C00F7A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.Debug|x64.ActiveCfg = Debug|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.Release|x64.ActiveCfg = Release|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2FBDCE20-3095-380B-92BE-2CEB3B591A77}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BF1FB228-6733-3D74-8A4C-D5E38567853B}.Debug|x64.ActiveCfg = Debug|x64
		{BF1FB228-6733-3D74-8A4C-D5E38567853B}.Release|x64.ActiveCfg = Release|x64
		{BF1FB228-6733-3D74-8A4C-D5E38567853B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BF1FB228-6733-3D74-8A4C-D5E38567853B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C2DC95EE-6BB9-3BFD-9E47-29E115B14C43}.Debug|x64.ActiveCfg = Debug|x64
		{C2DC95EE-6BB9-3BFD-9E47-29E115B14C43}.Release|x64.ActiveCfg = Release|x64
		{C2DC95EE-6BB9-3BFD-9E47-29E115B14C43}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C2DC95EE-6BB9-3BFD-9E47-29E115B14C43}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.Debug|x64.ActiveCfg = Debug|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.Release|x64.ActiveCfg = Release|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1350ADEA-93CA-329F-A31E-6E0BA4C61381}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Debug|x64.ActiveCfg = Debug|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Debug|x64.Build.0 = Debug|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Release|x64.ActiveCfg = Release|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.Release|x64.Build.0 = Release|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{329087FE-BA1C-36BD-A96C-7843776A63C5}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.Debug|x64.ActiveCfg = Debug|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.Debug|x64.Build.0 = Debug|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.Release|x64.ActiveCfg = Release|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.Release|x64.Build.0 = Release|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4DC1AD6B-0D2A-3743-B627-7740DB47A019}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.Debug|x64.ActiveCfg = Debug|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.Debug|x64.Build.0 = Debug|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.Release|x64.ActiveCfg = Release|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.Release|x64.Build.0 = Release|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4429CFB4-02BF-3D4F-AAA8-185A51F06914}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Debug|x64.ActiveCfg = Debug|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Debug|x64.Build.0 = Debug|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Release|x64.ActiveCfg = Release|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.Release|x64.Build.0 = Release|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{80C2E3E7-E328-3F5B-913B-74CE23B4B46D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Debug|x64.ActiveCfg = Debug|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Debug|x64.Build.0 = Debug|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Release|x64.ActiveCfg = Release|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.Release|x64.Build.0 = Release|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{697CAFA6-0EE1-3F69-AF49-3C5303F50007}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Debug|x64.ActiveCfg = Debug|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Debug|x64.Build.0 = Debug|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Release|x64.ActiveCfg = Release|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.Release|x64.Build.0 = Release|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{54D2F21E-E179-3772-B611-9EDF7E8CA92F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.Debug|x64.ActiveCfg = Debug|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.Debug|x64.Build.0 = Debug|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.Release|x64.ActiveCfg = Release|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.Release|x64.Build.0 = Release|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D8509A0F-24F8-37BF-9D68-CBE5D7EABA77}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.Debug|x64.ActiveCfg = Debug|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.Debug|x64.Build.0 = Debug|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.Release|x64.ActiveCfg = Release|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.Release|x64.Build.0 = Release|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0409B617-06D3-339E-A38B-0E8D55F28B4C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.Debug|x64.ActiveCfg = Debug|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.Debug|x64.Build.0 = Debug|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.Release|x64.ActiveCfg = Release|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.Release|x64.Build.0 = Release|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D42F0413-A84A-3B32-A23B-381224DF60E7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.Debug|x64.ActiveCfg = Debug|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.Debug|x64.Build.0 = Debug|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.Release|x64.ActiveCfg = Release|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.Release|x64.Build.0 = Release|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A224C536-65CC-3F30-8A5D-D13ECE8CFC2E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.Debug|x64.ActiveCfg = Debug|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.Debug|x64.Build.0 = Debug|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.Release|x64.ActiveCfg = Release|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.Release|x64.Build.0 = Release|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E98BC500-DB69-3EA0-A6FE-23A7AA167C3D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.Debug|x64.ActiveCfg = Debug|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.Debug|x64.Build.0 = Debug|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.Release|x64.ActiveCfg = Release|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.Release|x64.Build.0 = Release|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1141DA7B-ADB9-3983-974B-970F38AA644D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.Debug|x64.ActiveCfg = Debug|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.Debug|x64.Build.0 = Debug|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.Release|x64.ActiveCfg = Release|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.Release|x64.Build.0 = Release|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{60C3709A-B46C-313C-B088-C8095C05A0B1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.Debug|x64.ActiveCfg = Debug|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.Debug|x64.Build.0 = Debug|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.Release|x64.ActiveCfg = Release|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.Release|x64.Build.0 = Release|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3C972E0E-A360-33C6-9117-AD2273D6A3AA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.Debug|x64.ActiveCfg = Debug|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.Debug|x64.Build.0 = Debug|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.Release|x64.ActiveCfg = Release|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.Release|x64.Build.0 = Release|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E06C78BE-EFA6-31CC-99AD-409A1F8FC33E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {93E1DE25-5A1A-3E9F-9C71-C63513B41288}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
